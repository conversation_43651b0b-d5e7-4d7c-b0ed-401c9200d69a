require('dotenv').config();
const licenseManager = require('./license');

async function testLicense() {
  try {
    console.log('🔑 Whop Licentie Test\n');

    // Vraag om licentie key
    const licenseKey = process.argv[2];
    if (!licenseKey) {
      console.error('❌ Geen licentie key opgegeven');
      console.log('Gebruik: node test-license.js <licentie-key>');
      process.exit(1);
    }

    console.log('Test configuratie:');
    console.log('- Licentie key:', licenseKey);
    console.log('- Machine ID:', licenseManager.machineId);

    // Test de licentie
    console.log('\nValideren van licentie...');
    try {
      const isValid = await licenseManager.validateLicense(licenseKey);
      if (isValid) {
        console.log('\n✅ Licentie is geldig!');
        console.log('De licentie kan gebruikt worden in de applicatie.');
      } else {
        console.log('\n❌ Licentie is ongeldig');
      }
    } catch (error) {
      console.error('\n❌ Validatie fout:');
      console.error(error.message);
      if (error.response?.data) {
        console.log('\nAPI Response:');
        console.log(JSON.stringify(error.response.data, null, 2));
      }
    }

  } catch (error) {
    console.error('❌ Test fout:', error);
  }
}

testLicense(); 