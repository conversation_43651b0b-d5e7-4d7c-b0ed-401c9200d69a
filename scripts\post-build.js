const fs = require('fs-extra');
const path = require('path');
const archiver = require('archiver');

async function postBuild() {
  try {
    const pkg = require('../package.json');
    const distPath = path.join(__dirname, '../dist');
    const releasePath = path.join(__dirname, '../release');
    const rootPath = path.join(__dirname, '..');

    // Schoon en maak directories
    await fs.emptyDir(distPath);
    await fs.ensureDir(releasePath);

    // Essentiële directories
    const directories = [
      'config',
      'data',
      'logs'
    ];

    // Maak directories aan
    for (const dir of directories) {
      await fs.ensureDir(path.join(distPath, dir));
      
      // Voeg .gitkeep toe om lege mappen te behouden
      await fs.writeFile(
        path.join(distPath, dir, '.gitkeep'),
        '# Deze map wordt gebruikt door de applicatie'
      );
    }

    // Kopieer essentiële bestanden
    const filesToCopy = [
      {
        src: '.env.template',
        dest: '.env.template',
        content: `# API Keys
GOOGLE_PLACES_API_KEY=your_api_key_here
AIRTABLE_ACCESS_TOKEN=your_airtable_token_here
AIRTABLE_BASE_ID=your_base_id_here
AIRTABLE_TABLE_NAME=Results
AIRTABLE_VIEW_NAME=Grid view

# Firecrawl API Key (optioneel, voor website verrijking)
# Whop Configuratie
WHOP_API_KEY=your_whop_api_key_here`
      },
      {
        src: 'README.md',
        dest: 'README.md',
        content: `# Places API Scraper

## Installatie
1. Kopieer \`.env.template\` naar \`.env\`
2. Vul je API sleutels in in het \`.env\` bestand
3. Start de applicatie met \`places-scraper.exe\`

## Eerste gebruik
1. Voer je licentie key in (ontvangen na aankoop)
2. Kies een gebied om te scrapen of maak een nieuw grid
3. De resultaten worden opgeslagen in de \`data\` map

## Support
Bij problemen:
- Email: <EMAIL>
- Website: www.jouwbedrijf.com/support`
      }
    ];

    // Kopieer of maak bestanden aan
    for (const file of filesToCopy) {
      const destPath = path.join(distPath, file.dest);
      if (await fs.pathExists(path.join(rootPath, file.src))) {
        await fs.copy(path.join(rootPath, file.src), destPath);
      } else {
        await fs.writeFile(destPath, file.content);
      }
    }

    // Maak release ZIP
    const zipName = `places-scraper-v${pkg.version}.zip`;
    const zipPath = path.join(releasePath, zipName);
    const output = fs.createWriteStream(zipPath);
    const archive = archiver('zip', { zlib: { level: 9 } });

    output.on('close', () => {
      console.log('\n✅ Release package gemaakt!');
      console.log(`📦 Bestand: ${zipName}`);
      console.log(`📊 Grootte: ${(archive.pointer() / 1024 / 1024).toFixed(2)} MB`);
      console.log('\nBevat:');
      console.log('- places-scraper.exe (applicatie)');
      console.log('- .env.template (configuratie template)');
      console.log('- README.md (instructies)');
      console.log('- config/ (grid configuraties)');
      console.log('- data/ (output data)');
      console.log('- logs/ (logbestanden)');
    });

    archive.on('error', (err) => {
      throw err;
    });

    archive.pipe(output);
    archive.directory(distPath, false);
    await archive.finalize();

  } catch (error) {
    console.error('❌ Build fout:', error);
    process.exit(1);
  }
}

postBuild(); 