const { google } = require('googleapis');
const openaiService = require('./openaiService');
const firecrawlClient = require('./firecrawlClient');
const logger = require('./logger');
const axios = require('axios');
const airtableService = require('./airtableService');

class GoogleSearchService {
  constructor(options = {}) {
    this.options = {
      maxRetries: 3,
      delayBetweenRequests: 1000,
      // Functie zoektermen (bijv. een lijst van zoektermen waaraan de role moet voldoen)
      roleSearchTerms: [],
      ...options
    };
    
    this.client = null;
    this.searchEngineId = null;
    this.apiKey = null;
    this.websiteCache = new Map(); // Cache voor bedrijfswebsites
  }

  /**
   * <PERSON><PERSON><PERSON> of de gevonden role voldoet aan de zoektermen
   * @param {string} role - De role zoals geëxtraheerd door OpenAI
   * @param {string|string[]} searchTerms - De zoektermen waaraan de role moet voldoen
   * @returns {boolean} - True als de role voldoet, anders false
   */
  validateRole(role, searchTerms) {
    if (!role || !searchTerms) return false;
    
    const roleLower = role.toLowerCase();
    const terms = Array.isArray(searchTerms) ? searchTerms : [searchTerms];

    // Normaliseer de role voor betere matching
    const normalizedRole = roleLower
      .replace(/[^a-z0-9\s]/g, '')  // Verwijder speciale tekens
      .replace(/\s+/g, ' ')         // Normaliseer spaties
      .trim();

    // Check op specifieke zoektermen in de functietitel
    const hasSpecificTerm = terms.some(term => {
      // Normaliseer de zoekterm
      const normalizedTerm = term.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, ' ')
        .trim();

      // Check op exacte match of gedeeltelijke match
      if (normalizedRole === normalizedTerm) return true;
      
      // Check op woorden in de functietitel
      const roleWords = normalizedRole.split(' ');
      const termWords = normalizedTerm.split(' ');
      
      // Als alle woorden van de zoekterm voorkomen in de functietitel
      return termWords.every(word => 
        roleWords.some(roleWord => roleWord.includes(word) || word.includes(roleWord))
      );
    });

    // Log de validatie details
    logger.info('Role validatie details', {
      role,
      normalizedRole,
      hasSpecificTerm,
      searchTerms: terms
    });

    return hasSpecificTerm;
  }

  /**
   * Zoekt naar LinkedIn profielen
   * @param {Object} params - Zoekparameters
   * @param {string|string[]} params.jobTitles - Functietitels
   * @param {string} params.location - Locatie
   * @param {string} [params.company] - Bedrijfsnaam (optioneel)
   * @returns {Promise<Array>} - Gevonden profielen
   */
  async searchProfiles(params) {
    if (!this.client) await this.initialize();
    
    const { jobTitles, location, company } = params;
    const titles = Array.isArray(jobTitles) ? jobTitles : [jobTitles];

    // Splits titels in positieve en negatieve keywords
    const positiveTitles = [];
    const negativeKeywords = [];
    titles.forEach(title => {
      // Split op spaties, maar behoud samengestelde termen met quotes
      const parts = title.match(/(?:"[^"]+"|[^\s])+/g) || [title];
      parts.forEach(part => {
        const trimmed = part.trim();
        if (trimmed.startsWith('-')) {
          const neg = trimmed.replace(/^\-+/, '').replace(/^"|"$/g, '');
          if (neg) negativeKeywords.push(neg);
        } else {
          const pos = trimmed.replace(/^"|"$/g, '');
          if (pos) positiveTitles.push(pos);
        }
      });
    });

    // Update role zoektermen (indien opgegeven)
    if (params.roleSearchTerms) {
      this.options.roleSearchTerms = Array.isArray(params.roleSearchTerms) ? params.roleSearchTerms : [params.roleSearchTerms];
    }
    
    logger.info('Start profiel zoeken', { 
      jobTitles: titles, 
      positiveTitles,
      negativeKeywords,
      location, 
      company: company || 'niet gespecificeerd',
      roleSearchTerms: this.options.roleSearchTerms
    });

    try {
      // Maak één zoekopdracht met alle positieve functietitels
      const allTitles = positiveTitles.join(', ');
      // forceLocation is true als er een locatie is opgegeven, anders false
      const forceLocation = !!location;
      const query = this.buildSearchQuery(allTitles, location, company, forceLocation, negativeKeywords);
      const searchResults = await this.executeSearch(query);
      
      if (searchResults?.items) {
        // Voeg alle positieve zoektermen toe aan elk resultaat voor validatie
        searchResults.items.forEach(item => {
          item.searchTerms = positiveTitles;  // Gebruik alleen positieve zoektermen voor validatie
        });
      }

      logger.info(`${searchResults?.items?.length || 0} profielen gevonden`);
      return this.processResults(searchResults?.items || [], location);

    } catch (error) {
      logger.error('Fout bij zoeken profielen:', error);
      throw error;
    }
  }

  /**
   * Verwerkt de zoekresultaten
   * @param {Array} results - Ruwe zoekresultaten
   * @param {string} location - Locatie
   * @returns {Promise<Array>} - Verwerkte profielen
   */
  async processResults(results, location) {
    const processedResults = [];
    const seenProfiles = new Set();
    const companyWebsites = new Map();
    const BATCH_SIZE = 5;

    logger.info('Start verwerken zoekresultaten', {
      totalResults: results.length,
      location
    });

    // Verwerk resultaten in batches
    for (let i = 0; i < results.length; i += BATCH_SIZE) {
      const batch = results.slice(i, i + BATCH_SIZE);
      logger.info(`Verwerk batch ${i/BATCH_SIZE + 1}`, {
        batchSize: batch.length,
        totalProcessed: processedResults.length
      });

      // Verwerk batch
      const batchPromises = batch.map(async (result) => {
        try {
          // Extraheer basis informatie via OpenAI
          const basicInfo = await openaiService.extractBasicInfo(result);
          if (!basicInfo) {
            logger.warn('Geen basis informatie gevonden voor resultaat', {
              title: result.title
            });
            return null;
          }

          // Role validatie - gebruik alle zoektermen voor validatie
          const roleValid = this.validateRole(basicInfo.currentRole, result.searchTerms);
          if (!roleValid) {
            logger.warn('Profiel overgeslagen: role voldoet niet aan zoekcriteria', {
              role: basicInfo.currentRole,
              title: result.title,
              searchTerms: result.searchTerms
            });
            return null;
          }

          // Check op dubbele profielen
          const profileKey = `${basicInfo.name}|${basicInfo.company}`;
          if (seenProfiles.has(profileKey)) {
            logger.info('Dubbele profiel overgeslagen', {
              name: basicInfo.name,
              company: basicInfo.company
            });
            return null;
          }
          seenProfiles.add(profileKey);

          // Zoek website als nog niet in cache
          if (!companyWebsites.has(basicInfo.company)) {
            const website = await this.findCompanyWebsite(basicInfo.company, location);
            if (website) {
              companyWebsites.set(basicInfo.company, website);
              this.websiteCache.set(basicInfo.company, website);
            }
          }

          const website = companyWebsites.get(basicInfo.company);
          if (!website) {
            logger.warn('Geen website beschikbaar voor bedrijf', {
              company: basicInfo.company,
              name: basicInfo.name
            });
            return {
              ...basicInfo,
              website: null,
              linkedInUrl: result.link,
              enrichedData: null
            };
          }

          // Start direct een extractie job
          const job = {
            url: website.endsWith('/*') ? website : website + '/*',
            personInfo: {
              name: basicInfo.name,
              role: basicInfo.currentRole
            },
            basicInfo,
            linkedInUrl: result.link
          };

          // Start extractie en wacht op resultaat
          try {
            // Gebruik de juiste Firecrawl functie
            const startedJob = await firecrawlClient.startParallelExtractions([{
              url: job.url,
              personInfo: job.personInfo
            }]);
            
            if (!startedJob || !startedJob[0]?.jobId) {
              throw new Error('Geen geldige job ID ontvangen van Firecrawl');
            }

            const enrichedData = await firecrawlClient.pollExtractStatus(startedJob[0].jobId);
            
            // Schrijf direct weg naar Airtable
            const profile = {
              ...job.basicInfo,
              website: job.url,
              linkedInUrl: job.linkedInUrl,
              enrichedData
            };

            await airtableService.addProfileToAirtable(profile);
            logger.info('Profiel succesvol verwerkt en weggeschreven', {
              name: profile.name,
              company: profile.company,
              role: profile.currentRole
            });

            return profile;
          } catch (error) {
            logger.error('Fout bij extractie of wegschrijven', {
              name: job.personInfo.name,
              error: error.message,
              stack: error.stack
            });
            return {
              ...job.basicInfo,
              website: job.url,
              linkedInUrl: job.linkedInUrl,
              enrichedData: null
            };
          }

        } catch (error) {
          logger.error('Fout bij verwerken resultaat', {
            error: error.message,
            title: result.title,
            stack: error.stack
          });
          return null;
        }
      });

      // Wacht op verwerking van batch
      const batchResults = await Promise.all(batchPromises);
      processedResults.push(...batchResults.filter(r => r !== null));

      // Wacht even tussen batches
      if (i + BATCH_SIZE < results.length) {
        await new Promise(resolve => setTimeout(resolve, this.options.delayBetweenRequests));
      }
    }

    logger.info('Verwerking zoekresultaten voltooid', {
      totalResults: results.length,
      processedResults: processedResults.length,
      withEnrichedData: processedResults.filter(r => r.enrichedData !== null).length
    });

    return processedResults;
  }

  /**
   * Berekent een match score voor een domein tegen een bedrijfsnaam
   * @param {string} domain - Het domein om te checken
   * @param {string} companyName - De bedrijfsnaam
   * @param {Object} result - Het Google zoekresultaat
   * @returns {Object} - Score object met details
   */
  calculateMatchScore(domain, companyName, result) {
    const scores = {
      exactMatch: 0,        // Exacte match (100 punten)
      domainMatch: 0,       // Match in domeinnaam (80 punten)
      pathMatch: 0,         // Match in URL pad (40 punten)
      titleMatch: 0,        // Match in titel (60 punten)
      snippetMatch: 0,      // Match in snippet (30 punten)
      acronymMatch: 0,      // Match op acroniem (50 punten)
      wordMatch: 0,         // Match op losse woorden (20 punten per woord)
      blacklistPenalty: 0,  // Straf voor blacklist (direct afgekeurd)
      total: 0
    };

    // Normaliseer bedrijfsnaam
    const normalizedCompanyName = this.normalizeCompanyName(companyName);
    const companyWords = this.getCompanyWords(normalizedCompanyName);
    const companyAcronym = this.getCompanyAcronym(companyWords);

    // Extra normalisatie voor domein en bedrijfsnaam (alleen letters/cijfers, lowercase)
    const normDomain = domain.replace(/[^a-z0-9]/gi, '').toLowerCase();
    const normCompany = normalizedCompanyName.replace(/[^a-z0-9]/gi, '').toLowerCase();

    // Check blacklist eerst
    if (this.isBlacklisted(domain)) {
      scores.blacklistPenalty = -1000;
      scores.total = -1000;
      return scores;
    }

    // 1. Exacte match (100 punten) - tolerant voor streepjes/spaties
    if (normDomain === normCompany || normDomain === `${normCompany}nl`) {
      scores.exactMatch = 100;
    }

    // 2. Domein match (80 punten) - tolerant voor streepjes/spaties
    if (normDomain.includes(normCompany)) {
      scores.domainMatch = 80;
    }

    // 3. Path match (40 punten)
    const url = result.link.toLowerCase();
    const path = url.split('/').slice(3).join('/');
    if (path.includes(normCompany)) {
      scores.pathMatch = 40;
    }

    // 4. Titel match (60 punten)
    const title = result.title.toLowerCase();
    if (title.includes(normCompany)) {
      scores.titleMatch = 60;
    }

    // 5. Snippet match (30 punten)
    const snippet = result.snippet?.toLowerCase() || '';
    if (snippet.includes(normCompany)) {
      scores.snippetMatch = 30;
    }

    // 6. Acroniem match (50 punten)
    if (companyAcronym.length >= 2 && normDomain.includes(companyAcronym)) {
      scores.acronymMatch = 50;
    }

    // 7. Woord match (20 punten per woord)
    const matchingWords = companyWords.filter(word => 
      word.length >= 2 && normDomain.includes(word)
    );
    scores.wordMatch = matchingWords.length * 20;

    // Bereken totaalscore
    scores.total = Object.values(scores).reduce((sum, score) => sum + score, 0);

    // Log de scoring details
    logger.info('Website match scoring', {
      domain,
      companyName,
      normalizedCompanyName,
      normDomain,
      normCompany,
      companyWords,
      companyAcronym,
      scores,
      url: result.link,
      title: result.title,
      snippet: result.snippet?.substring(0, 100) + '...'
    });

    return scores;
  }

  /**
   * Normaliseert een bedrijfsnaam voor matching
   * @param {string} companyName - De bedrijfsnaam
   * @returns {string} - Genormaliseerde bedrijfsnaam
   */
  normalizeCompanyName(companyName) {
    // Verwijder eerst algemene prefixen/suffixen
    let name = companyName
      .toLowerCase()
      .replace(/^gemeente\s+/i, '')
      .replace(/\s+stichting\s+/i, '')
      .replace(/\s+foundation\s+/i, '')
      .replace(/\s+b\.v\.$/i, '')
      .replace(/\s+bv$/i, '')
      .replace(/\s+n\.v\.$/i, '')
      .replace(/\s+nv$/i, '')
      .trim();

    // Splits in woorden en normaliseer elk woord
    const words = name.split(/\s+/).map(word => 
      word
        .replace(/[&\s.,'"()]/g, '')
        .replace(/[éèêë]/g, 'e')
        .replace(/[àáâä]/g, 'a')
        .replace(/[ìíîï]/g, 'i')
        .replace(/[òóôö]/g, 'o')
        .replace(/[ùúûü]/g, 'u')
        .replace(/[ç]/g, 'c')
    );

    // Verwijder lege woorden en algemene woorden
    const filteredWords = words.filter(word => 
      word.length > 0 && !this.isCommonWord(word)
    );

    // Als er geen woorden over zijn, gebruik de originele naam
    if (filteredWords.length === 0) {
      return name.replace(/[&\s.,'"()]/g, '');
    }

    // Als het een acroniem is (bijv. G4S), behoud het als één woord
    if (name.match(/^[a-z0-9]{2,}$/i)) {
      return name.toLowerCase();
    }

    // Anders, gebruik de gefilterde woorden
    return filteredWords.join('');
  }

  /**
   * Haalt relevante woorden uit een bedrijfsnaam
   * @param {string} normalizedName - Genormaliseerde bedrijfsnaam
   * @returns {string[]} - Array van relevante woorden
   */
  getCompanyWords(normalizedName) {
    // Als het een acroniem is, behoud het als één woord
    if (normalizedName.match(/^[a-z0-9]{2,}$/i)) {
      return [normalizedName];
    }

    // Splits in woorden en filter
    return normalizedName
      .split(/(?=[A-Z])|[-_\s]/)  // Splits op hoofdletters, streepjes, underscores en spaties
      .map(word => word.toLowerCase())
      .filter(word => word.length >= 2)  // Verlaagd van 4 naar 2 tekens
      .filter(word => !this.isCommonWord(word));
  }

  /**
   * Maakt een acroniem van een bedrijfsnaam
   * @param {string[]} words - Array van bedrijfsnaam woorden
   * @returns {string} - Acroniem
   */
  getCompanyAcronym(words) {
    // Als er maar één woord is en het is een acroniem, gebruik dat
    if (words.length === 1 && words[0].match(/^[a-z0-9]{2,}$/i)) {
      return words[0].toLowerCase();
    }

    // Anders, maak acroniem van woorden
    return words
      .filter(word => word.length >= 2)  // Verlaagd van 4 naar 2 tekens
      .map(word => word[0].toLowerCase())
      .join('');
  }

  /**
   * Checkt of een woord een algemeen woord is
   * @param {string} word - Het woord om te checken
   * @returns {boolean} - True als het een algemeen woord is
   */
  isCommonWord(word) {
    const commonWords = [
      'bedrijf', 'company', 'groep', 'group', 'holding', 'international',
      'nederland', 'netherlands', 'services', 'solutions', 'systems',
      'technologies', 'technology', 'management', 'consulting', 'consultancy',
      'advies', 'adviesbureau', 'bureau', 'office', 'kantoor', 'centrum',
      'center', 'centre', 'stichting', 'foundation', 'vereniging', 'association'
    ];
    return commonWords.includes(word.toLowerCase());
  }

  /**
   * Checkt of een domein in de blacklist staat
   * @param {string} domain - Het domein om te checken
   * @returns {boolean} - True als het domein in de blacklist staat
   */
  isBlacklisted(domain) {
    const blacklist = [
      // Job sites en directories
      'indeed.com', 'nl.indeed.com', 'jobbird.com', 'glassdoor.nl', 'intermediair.nl',
      'nationalevacaturebank.nl', 'monsterboard.nl', 'werkzoeken.nl', 'inwork.nl',
      'trustpilot.com', 'jobrapido.com', 'jobserve.com', 'jobg8.com', 'joblift.nl',
      'jobsonline.nl', 'jobtrack.nl', 'jobweb.nl', 'jobworld.nl', 'jobzone.nl',
      'jooble.org', 'jobstreet.nl', 'jobsite.nl', 'jobnet.nl', 'jobnews.nl',
      // Social media en platforms
      'linkedin.com', 'facebook.com', 'twitter.com', 'instagram.com', 'pinterest.com',
      'google.com', 'bing.com', 'tripadvisor.com', 'yelp.com', 'yellowpages',
      // Overige platforms
      'kvk.nl', 'feedbackcompany.com', 'transfirm.nl', 'rocketreach.co',
      // Grote bedrijven (consultancy, tech, etc.)
      'accenture.com', 'volkerwessels.com', 'kpmg.com', 'deloitte.com', 'ey.com',
      'pwc.com', 'microsoft.com', 'amazon.com', 'apple.com', 'ibm.com', 'oracle.com'
    ];

    return blacklist.some(bad => domain.includes(bad));
  }

  /**
   * Zoekt naar de officiële website van een bedrijf via Google Custom Search
   * @param {string} companyName - Bedrijfsnaam
   * @param {string} [location] - Locatie (optioneel)
   * @returns {Promise<string|null>} - Website URL of null
   */
  async findCompanyWebsite(companyName, location) {
    try {
      if (!companyName) {
        logger.info('Geen bedrijfsnaam opgegeven voor website zoeken');
        return null;
      }

      // Controleer of het bedrijf bekend is
      const unknownPatterns = [
        'onbekend', 'niet bekend', 'unknown', 'niet beschikbaar',
        'niet beschikbaar', 'n/a', 'niet van toepassing'
      ];

      const isUnknown = unknownPatterns.some(pattern => 
        companyName.toLowerCase().includes(pattern)
      );

      if (isUnknown) {
        logger.info(`Bedrijf '${companyName}' is onbekend, overslaan van website zoeken`);
        return null;
      }

      // Normaliseer bedrijfsnaam voor zoekopdracht
      const normalizedName = this.normalizeCompanyName(companyName);
      const companyWords = this.getCompanyWords(normalizedName);
      const companyAcronym = this.getCompanyAcronym(companyWords);

      // Bouw zoekopdrachten in volgorde van relevantie
      const searchQueries = [
        // 1. Exacte bedrijfsnaam (meest specifiek)
        `"${companyName}" (website OR "officiële website" OR "bedrijfswebsite" OR "contact" OR "contactgegevens" OR "over ons" OR "over het bedrijf")`,
        // 2. Alleen bedrijfsnaam (zonder quotes)
        `${companyName} (website OR "officiële website" OR "bedrijfswebsite" OR "contact" OR "contactgegevens" OR "over ons" OR "over het bedrijf")`,
        // 4. Eerste woord van bedrijfsnaam (voor gemeentes etc.)
        companyWords.length > 0 ? 
          `"${companyWords[0]}" (website OR "officiële website" OR "bedrijfswebsite" OR "contact" OR "contactgegevens" OR "over ons" OR "over het bedrijf")` : null,
        // 3. Acroniem (als die bestaat)
        companyAcronym.length >= 2 ? 
          `"${companyAcronym}" (website OR "officiële website" OR "bedrijfswebsite" OR "contact" OR "contactgegevens" OR "over ons" OR "over het bedrijf")` : null
      ].filter(q => q !== null);

      // Voeg exclusies toe aan alle queries
      const exclusions = ' -site:linkedin.com -site:facebook.com -site:twitter.com -site:instagram.com -site:pinterest.com -site:yelp.com -site:yellowpages -site:kvk.nl -site:google.com -site:tripadvisor.com -site:bing.com -site:feedbackcompany.com -site:transfirm.nl';
      searchQueries.forEach((q, i) => {
        searchQueries[i] = q + exclusions;
      });

      logger.info('Start zoeken bedrijfswebsite met sequentiële queries', {
        company: companyName,
        normalizedName,
        companyWords,
        companyAcronym,
        queries: searchQueries
      });

      // Dynamische dateRestrict filters: eerst maand, dan jaar, dan alles
      const dateRestrictOptions = ['m[1]', 'y[1]', ''];
      let bestMatch = null;
      let bestScore = 0;
      let bestMatchType = 'none';
      let bestResults = [];

      // Voer de queries sequentieel uit
      for (const query of searchQueries) {
        for (const dateRestrict of dateRestrictOptions) {
          let allResults = [];
          try {
            const response = await axios.get('https://www.googleapis.com/customsearch/v1', {
              params: {
                key: this.apiKey,
                cx: this.searchEngineId,
                q: query,
                num: 5,
                fields: 'items(title,link,snippet),queries,searchInformation',
                hl: 'nl',
                gl: 'nl',
                safe: 'off',
                lr: 'lang_nl',
                sort: '',
                filter: '0',
                ...(dateRestrict ? { dateRestrict } : {})
              },
              headers: {
                'Accept-Encoding': 'gzip',
                'User-Agent': 'LeadFinder/1.0 (gzip)'
              }
            });

            if (response.data?.items?.length) {
              allResults = response.data.items;
            }

            // Wacht even tussen queries
            await new Promise(resolve => setTimeout(resolve, this.options.delayBetweenRequests));
          } catch (error) {
            logger.warn('Fout bij uitvoeren zoekopdracht', {
              query,
              dateRestrict,
              error: error.message
            });
          }

          if (!allResults.length) {
            logger.info('Geen resultaten gevonden voor bedrijfswebsite', {
              company: companyName,
              query,
              dateRestrict
            });
            continue;
          }

          // Score alle resultaten
          const scoredResults = allResults.map(result => {
            try {
              const domain = new URL(result.link).hostname.replace('www.', '');
              const scores = this.calculateMatchScore(domain, companyName, result);
              return { result, domain, scores };
            } catch (e) {
              logger.warn('Fout bij scoren resultaat', {
                error: e.message,
                link: result.link
              });
              return null;
            }
          }).filter(r => r !== null);

          // Verwijder dubbele domeinen (houd hoogste score)
          const uniqueDomains = new Map();
          scoredResults.forEach(r => {
            if (!uniqueDomains.has(r.domain) || uniqueDomains.get(r.domain).scores.total < r.scores.total) {
              uniqueDomains.set(r.domain, r);
            }
          });

          // Sorteer op score
          const sortedResults = Array.from(uniqueDomains.values())
            .sort((a, b) => b.scores.total - a.scores.total);

          // Log de top 3 resultaten
          logger.info('Top 3 website matches', {
            company: companyName,
            query,
            dateRestrict,
            matches: sortedResults.slice(0, 3).map(r => ({
              domain: r.domain,
              score: r.scores.total,
              details: r.scores,
              matchType: r.scores.exactMatch ? 'exact' :
                        r.scores.domainMatch ? 'domain' :
                        r.scores.titleMatch ? 'title' :
                        r.scores.acronymMatch ? 'acronym' :
                        'other'
            }))
          });

          // Alleen accepteren als er een goede match is (minimaal 50 punten)
          const currentBest = sortedResults[0];
          if (currentBest && currentBest.scores.total >= 50) {
            logger.info('Website gevonden met goede match score', {
              company: companyName,
              domain: currentBest.domain,
              score: currentBest.scores.total,
              details: currentBest.scores,
              matchType: currentBest.scores.exactMatch ? 'exact' :
                        currentBest.scores.domainMatch ? 'domain' :
                        currentBest.scores.titleMatch ? 'title' :
                        currentBest.scores.acronymMatch ? 'acronym' :
                        'other',
              query,
              dateRestrict
            });
            return 'https://' + currentBest.domain;
          }

          // Onthoud de beste tot nu toe (voor logging)
          if (currentBest && currentBest.scores.total > bestScore) {
            bestMatch = currentBest;
            bestScore = currentBest.scores.total;
            bestMatchType = currentBest.scores.exactMatch ? 'exact' :
                            currentBest.scores.domainMatch ? 'domain' :
                            currentBest.scores.titleMatch ? 'title' :
                            currentBest.scores.acronymMatch ? 'acronym' :
                            'other';
            bestResults = sortedResults;
          }
        }
      }

      logger.warn('Geen website gevonden met voldoende match score na alle sequentiële queries', {
        company: companyName,
        bestScore,
        bestMatchType,
        bestMatch: bestMatch ? bestMatch.domain : null,
        top3: bestResults.slice(0, 3).map(r => ({
          domain: r.domain,
          score: r.scores.total,
          details: r.scores
        }))
      });
      return null;

    } catch (error) {
      logger.error('Fout bij zoeken bedrijfswebsite', {
        company: companyName,
        error: error.message
      });
      return null;
    }
  }

  /**
   * Bouwt de zoekopdracht op basis van parameters
   * @param {string} title - Functietitel
   * @param {string} location - Locatie
   * @param {string} [company] - Bedrijfsnaam (optioneel)
   * @param {boolean} [forceLocation=false] - Voeg locatie toe aan de zoekopdracht
   * @param {string[]} [excludeKeywords=[]] - Negatieve keywords voor uitsluiting
   * @returns {string} - Zoekopdracht
   */
  buildSearchQuery(title, location, company, forceLocation = false, excludeKeywords = []) {
    let query = 'site:nl.linkedin.com/in ';

    // Verwerk functietitel(s) als simpele OR-query
    const terms = title.split(',')
      .map(term => term.trim())
      .filter(term => term)
      // Verwijder dubbele termen (case-insensitive)
      .filter((term, index, self) => 
        index === self.findIndex(t => t.toLowerCase() === term.toLowerCase())
      )
      .map(term => (term.includes(' ') || term.includes('-')) ? `"${term}"` : term)
      .join(' OR ');
    
    query += `(${terms})`;

    // Voeg locatie toe alleen als forceLocation true is
    if (forceLocation && location) {
      query += ` "${location}"`;
    }

    // Voeg bedrijfsnaam toe
    if (company) {
      query += ` "${company}"`;
    }

    // Voeg negatieve keywords toe
    if (excludeKeywords && excludeKeywords.length > 0) {
      query += ' ' + excludeKeywords.map(kw => kw.includes(' ') ? `-"${kw}"` : `-${kw}`).join(' ');
    }

    // Voeg exclusies toe
    query += ' -premium -site:uk.linkedin.com -site:de.linkedin.com -site:fr.linkedin.com -site:es.linkedin.com -site:it.linkedin.com -site:pt.linkedin.com -site:be.linkedin.com';

    logger.info('Gebouwde zoekopdracht', { 
      query,
      originalTerms: title.split(',').map(t => t.trim()),
      processedTerms: terms,
      excludeKeywords
    });
    return query;
  }

  /**
   * Genereert zoekopdrachten met verschillende datum ranges
   * @param {string} query - Originele zoekopdracht
   * @returns {Array<string>} - Array van zoekopdrachten met verschillende datum ranges
   */
  generateDateRangeQueries(query) {
    const dateRanges = [
      'y[1]',    // Laatste jaar
      'm[6]',    // Laatste 6 maanden
      'm[3]',    // Laatste 3 maanden
      'm[1]',    // Laatste maand
      'w[1]',    // Laatste week
      ''         // Geen datum filter
    ];

    return dateRanges.map(range => {
      // Verwijder bestaande dateRestrict parameter als die er is
      const cleanQuery = query.replace(/dateRestrict:[^ ]+/, '').trim();
      return range ? `${cleanQuery} dateRestrict:${range}` : cleanQuery;
    });
  }

  /**
   * Voert de zoekopdracht uit
   * @param {string} query - Zoekopdracht
   * @returns {Promise<Object>} - Zoekresultaten
   */
  async executeSearch(query) {
    try {
      const allResults = [];
      const seenUrls = new Set();
      const dateRangeQueries = this.generateDateRangeQueries(query);

      logger.info('Start zoeken met datum ranges', {
        originalQuery: query,
        totalRanges: dateRangeQueries.length
      });

      for (const dateQuery of dateRangeQueries) {
        let startIndex = 1;
        const maxResultsPerPage = 10;
        let hasMoreResults = true;

        logger.info('Zoek met datum range', {
          query: dateQuery,
          currentResults: allResults.length
        });

        while (hasMoreResults) {
          const results = await google.customsearch('v1').cse.list({
            auth: this.apiKey,
            cx: this.searchEngineId,
            q: dateQuery,
            start: startIndex,
            num: maxResultsPerPage,
            fields: 'items(title,link,snippet,pagemap),queries,searchInformation',
            hl: 'nl',
            gl: 'nl',
            safe: 'off',
            lr: 'lang_nl',
            filter: '0'
          });

          if (results.data?.items?.length) {
            // Filter dubbele URLs
            for (const item of results.data.items) {
              if (!seenUrls.has(item.link)) {
                seenUrls.add(item.link);
                allResults.push(item);
              }
            }
            
            const totalResults = results.data.searchInformation?.totalResults || 0;
            hasMoreResults = allResults.length < totalResults && (startIndex + maxResultsPerPage) <= 100;
            
            if (hasMoreResults) {
              startIndex += maxResultsPerPage;
              await new Promise(resolve => setTimeout(resolve, this.options.delayBetweenRequests));
            }
          } else {
            hasMoreResults = false;
          }
        }

        // Wacht even tussen verschillende datum ranges
        if (dateRangeQueries.length > 1) {
          await new Promise(resolve => setTimeout(resolve, this.options.delayBetweenRequests * 2));
        }
      }

      logger.info('Zoekopdracht voltooid', {
        originalQuery: query,
        totalResults: allResults.length,
        uniqueResults: seenUrls.size
      });

      return { items: allResults };
    } catch (error) {
      logger.error('Fout bij uitvoeren zoekopdracht:', error);
      throw error;
    }
  }

  /**
   * Initialiseert de Google Custom Search Engine client
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      const apiKey = process.env.GOOGLE_SEARCH_API_KEY;
      const cseId = process.env.GOOGLE_CSE_ID;
      if (!apiKey || !cseId) {
        throw new Error('Google API configuratie ontbreekt (GOOGLE_SEARCH_API_KEY of GOOGLE_CSE_ID)');
      }
      this.searchEngineId = cseId;
      this.apiKey = apiKey;
      logger.info('Google Search service geïnitialiseerd');
    } catch (error) {
      logger.error('Fout bij initialiseren:', error);
      throw error;
    }
  }
}

module.exports = new GoogleSearchService(); 