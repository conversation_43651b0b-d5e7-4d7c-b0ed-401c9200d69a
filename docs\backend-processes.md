# Backend Processen - Places API Scraper

## Inhoudsopgave
1. [Initialisatie](#initialisatie)
2. [Data Verzamelingsprocessen](#data-verzamelingsprocessen)
3. [Data Verrijkingsprocessen](#data-verrijkingsprocessen)
4. [Data Opslagprocessen](#data-opslagprocessen)
5. [Monitoring en Foutafhandeling](#monitoring-en-foutafhandeling)
6. [Google Search Integratie](#google-search-integratie)

## Initialisatie

### 1. Applicatie Start
1. **Directory Structuur Opzet**
   - Controleer en maak benodigde directories aan:
     - `/config` - Voor configuratiebestanden
     - `/data` - Voor cache en tijdelijke data
     - `/logs` - Voor logbestanden
     - `/dist` - Voor gecompileerde bestanden

2. **Configuratie Laden**
   - Laad environment variables uit `.env` bestand
   - Valideer verplichte configuratie:
     - Google Places API key
     - Airtable credentials
     - Firecrawl API key
   - Initialiseer logging systeem

3. **Service Initialisatie**
   - Start PlacesScraper instantie
   - Initialiseer GridGenerator
   - Verbind met externe services:
     - Google Places API
     - Airtable
     - Firecrawl

### 2. Cache Systeem
1. **Cache Initialisatie**
   - Laad bestaande cache bestanden:
     - `processed_places.json`
     - `processed_urls.json`
   - Valideer cache integriteit
   - Maak nieuwe cache aan indien niet bestaand

## Data Verzamelingsprocessen

### 1. URL-gebaseerde Verwerking
1. **URL Voorbereiding**
   ```javascript
   // Chronologische stappen
   1. URL normalisatie
   2. Protocol validatie (http/https)
   3. Domein extractie
   4. Cache controle
   ```

2. **Data Verzameling**
   ```javascript
   // Volgorde van operaties
   1. Firecrawl verrijking starten
   2. Bedrijfsnaam extractie
   3. Places API zoekopdracht
   4. Resultaat validatie
   ```

3. **Data Verwerking**
   ```javascript
   // Verwerkingsstappen
   1. Combineer Firecrawl data
   2. Verwerk Places API resultaten
   3. Valideer gecombineerde data
   4. Bereid voor op opslag
   ```

### 2. Naam-gebaseerde Verwerking
1. **Zoekopdracht Voorbereiding**
   ```javascript
   // Chronologische stappen
   1. Naam normalisatie
   2. Land validatie
   3. Cache controle
   4. Zoekparameters opstellen
   ```

2. **Places API Zoekopdracht**
   ```javascript
   // Volgorde van operaties
   1. Initiële zoekopdracht
   2. Resultaat filtering
   3. Detail ophalen
   4. Website extractie
   ```

3. **Data Verrijking**
   ```javascript
   // Verrijkingsstappen
   1. Website validatie
   2. Firecrawl verrijking
   3. Data combinatie
   4. Validatie
   ```

### 3. Grid-gebaseerde Verwerking
1. **Grid Generatie**
   ```javascript
   // Chronologische stappen
   1. Middelpunt validatie
   2. Radius berekening
   3. Grid punt generatie
   4. Zoekparameters per punt
   ```

2. **Per Grid Punt Verwerking**
   ```javascript
   // Volgorde van operaties
   1. Places API zoekopdracht
   2. Resultaat verzameling
   3. Paginering afhandeling
   4. Rate limiting
   ```

3. **Data Aggregatie**
   ```javascript
   // Aggregatiestappen
   1. Duplicaat detectie
   2. Data normalisatie
   3. Batch voorbereiding
   4. Cache update
   ```

## Data Verrijkingsprocessen

### 1. Firecrawl Verrijking
1. **Website Analyse**
   ```javascript
   // Chronologische stappen
   1. Website validatie
   2. Content extractie
   3. Bedrijfsinformatie ophalen
   4. Data structurering
   ```

2. **Data Combinatie**
   ```javascript
   // Combinatiestappen
   1. Places API data
   2. Firecrawl data
   3. Validatie
   4. Conflict resolutie
   ```

### 2. Places API Verrijking
1. **Detail Verzameling**
   ```javascript
   // Verzamelingsstappen
   1. Basis informatie
   2. Contact gegevens
   3. Locatie data
   4. Reviews en ratings
   ```

2. **Data Validatie**
   ```javascript
   // Validatiestappen
   1. Verplichte velden
   2. Data type controle
   3. Formaat validatie
   4. Consistentie check
   ```

## Data Opslagprocessen

### 1. Airtable Integratie
1. **Data Voorbereiding**
   ```javascript
   // Voorbereidingsstappen
   1. Veld mapping
   2. Data transformatie
   3. Batch groepering
   4. Rate limit controle
   ```

2. **Opslag Proces**
   ```javascript
   // Opslagstappen
   1. Batch creatie
   2. Error handling
   3. Retry logica
   4. Succes validatie
   ```

### 2. Cache Management
1. **Cache Updates**
   ```javascript
   // Update stappen
   1. Nieuwe items toevoegen
   2. Cache validatie
   3. Periodieke opslag
   4. Integriteit check
   ```

2. **Cache Optimalisatie**
   ```javascript
   // Optimalisatiestappen
   1. Duplicaat verwijdering
   2. Cache compressie
   3. Oude items opruimen
   4. Performance optimalisatie
   ```

## Monitoring en Foutafhandeling

### 1. Logging Systeem
1. **Log Niveaus**
   ```javascript
   // Logging stappen
   1. Debug logging
   2. Info logging
   3. Warning logging
   4. Error logging
   ```

2. **Log Rotatie**
   ```javascript
   // Rotatiestappen
   1. Log bestandsgrootte controle
   2. Oude logs archiveren
   3. Nieuwe log starten
   4. Backup maken
   ```

### 2. Error Handling
1. **Fout Detectie**
   ```javascript
   // Detectiestappen
   1. API fouten
   2. Netwerk fouten
   3. Validatie fouten
   4. Systeem fouten
   ```

2. **Fout Herstel**
   ```javascript
   // Herstelstappen
   1. Retry logica
   2. Fallback opties
   3. Error rapportage
   4. Systeem herstel
   ```

### 3. Performance Monitoring
1. **Metrics Verzameling**
   ```javascript
   // Verzamelingsstappen
   1. Response tijden
   2. Succes ratio's
   3. Resource gebruik
   4. Cache hit ratio's
   ```

2. **Rapportage**
   ```javascript
   // Rapportagestappen
   1. Dagelijkse statistieken
   2. Error rapporten
   3. Performance metrics
   4. Gebruiksstatistieken
   ```

## Google Search Integratie

### 1. Functie-gebaseerde Zoekopdracht
1. **Zoekopdracht Voorbereiding**
   ```javascript
   // Chronologische stappen
   1. Functietitel normalisatie
   2. Locatie validatie (optioneel)
   3. Zoekparameters opstellen
   4. API key management
   ```

2. **Google Search Verwerking**
   ```javascript
   // Volgorde van operaties
   1. Initiële zoekopdracht
   2. Resultaat filtering
   3. Paginering afhandeling
   4. Rate limiting
   ```

3. **Resultaat Verwerking**
   ```javascript
   // Verwerkingsstappen
   1. LinkedIn profiel extractie
   2. Bedrijfsinformatie ophalen
   3. Firecrawl verrijking
   4. Data validatie
   ```

### 2. Bedrijfswebsite Zoekopdracht
1. **Website Detectie**
   ```javascript
   // Detectiestappen
   1. Bedrijfsnaam normalisatie
   2. Domein validatie
   3. Blacklist filtering
   4. Relevante score berekening
   ```

2. **Resultaat Filtering**
   ```javascript
   // Filterstappen
   1. Sociale media uitsluiting
   2. Directory uitsluiting
   3. Domein matching
   4. Prioriteit sortering
   ```

3. **Website Validatie**
   ```javascript
   // Validatiestappen
   1. Domein verificatie
   2. Bedrijfsnaam matching
   3. Acroniem matching
   4. Fallback opties
   ```

### 3. Firecrawl Integratie
1. **Data Verrijking**
   ```javascript
   // Verrijkingsstappen
   1. Website analyse
   2. Bedrijfsinformatie extractie
   3. Contact gegevens ophalen
   4. Data structurering
   ```

2. **Kosten Optimalisatie**
   ```javascript
   // Optimalisatiestappen
   1. OpenAI token tracking
   2. Batch verwerking
   3. Cache gebruik
   4. Rate limiting
   ```

### 4. Data Combinatie
1. **Resultaat Integratie**
   ```javascript
   // Integratiestappen
   1. Google Search data
   2. Firecrawl data
   3. Places API data
   4. Duplicaat detectie
   ```

2. **Data Normalisatie**
   ```javascript
   // Normalisatiestappen
   1. Veld mapping
   2. Formaat standaardisatie
   3. Taal normalisatie
   4. Validatie
   ```

### 5. Performance Optimalisatie
1. **Zoekopdracht Optimalisatie**
   ```javascript
   // Optimalisatiestappen
   1. Query optimalisatie
   2. Resultaat caching
   3. Batch verwerking
   4. Parallelle verwerking
   ```

2. **Resource Management**
   ```javascript
   // Management stappen
   1. API key rotatie
   2. Rate limit monitoring
   3. Kosten tracking
   4. Cache management
   ```

### 6. Foutafhandeling
1. **Error Management**
   ```javascript
   // Management stappen
   1. API fouten
   2. Netwerk fouten
   3. Validatie fouten
   4. Retry logica
   ```

2. **Fallback Strategieën**
   ```javascript
   // Strategiestappen
   1. Alternatieve zoekopdrachten
   2. Vereenvoudigde matching
   3. Cache fallback
   4. Error rapportage
   ```

## Hybride Firecrawl Modus

### 1. Overzicht Hybride Modus
De hybride modus biedt een tussenweg tussen volledige Firecrawl verrijking en alleen Places API data:

```javascript
// Hybride configuratie
const scraper = new PlacesScraper({
  enableFirecrawlEnrichment: true,
  firecrawlMode: 'limited' // Hybride modus
});
```

### 2. Hybride Data Mapping
1. **Basis Places API Velden**
   ```javascript
   // Standaard velden uit Places API
   - company_name
   - phone
   - website
   - address_street, address_city, address_postcode
   - notes (rating, reviews, types)
   ```

2. **Extra Firecrawl Velden (Beperkt)**
   ```javascript
   // Alleen deze 4 extra velden uit Firecrawl
   - decision_maker_name (contactpersoon naam)
   - role_title (functietitel)
   - email (contact email)
   - summary (bedrijfssamenvatting)
   ```

### 3. Kosten & Performance Vergelijking

| Modus | Kosten per bedrijf | Tijd per bedrijf | Contactgegevens | Use Case |
|-------|-------------------|------------------|-----------------|----------|
| **Alleen Places API** | ~€0.01 | 1-3 sec | 30-50% | Marktonderzoek |
| **Hybride Modus** | ~€0.05-0.15 | 15-30 sec | 70-80% | Lead generatie |
| **Volledige Firecrawl** | ~€0.10-0.50 | 30-60 sec | 90%+ | Sales prospecting |

### 4. Implementatie Details
1. **Mapping Functie**
   ```javascript
   // Nieuwe mapping functie voor hybride modus
   mapPlacesWithLimitedFirecrawlToAirtableFields(placeDetails, enrichedData)
   ```

2. **CLI Opties**
   ```bash
   # Interactieve modus
   node src/cli.js
   # Kies: "Zoeken op locatie (hybride - beperkte Firecrawl)"

   # Command line modus
   node src/cli.js --hybrid-enrich -c "52.3676,4.9041" -r 5 -k "restaurant"
   ```

3. **Test Scripts**
   ```bash
   # Test hybride mapping
   node src/test-hybrid-mapping.js

   # Test hybride scraping
   node src/test-grid-hybrid.js
   ```

## Conclusie
De backend processen zijn uitgebreid met:
- **Google Search functionaliteit** voor LinkedIn profielen
- **Hybride Firecrawl modus** voor kosteneffectieve verrijking
- **Flexibele configuratie** voor verschillende use cases

De drie modi bieden verschillende balansen tussen kosten, snelheid en datakwaliteit:
1. **Snel & Goedkoop**: Alleen Places API
2. **Gebalanceerd**: Hybride modus (Places + beperkte Firecrawl)
3. **Volledig & Duur**: Volledige Firecrawl verrijking

De integratie met Firecrawl zorgt voor verrijkte data terwijl de kosten worden geoptimaliseerd door slim gebruik van caching, batch verwerking en de nieuwe hybride modus.