@echo off
echo ===================================
echo   Google Places API Scraper Tool
echo ===================================
echo.

REM Controleer of benodigde mappen bestaan
if not exist config mkdir config
if not exist data mkdir data
if not exist logs mkdir logs

REM Controleer of .env bestand bestaat
if not exist .env (
  echo WAARSCHUWING: .env bestand niet gevonden!
  echo Een voorbeeld .env bestand aanmaken...
  echo # Google Places API en Airtable configuratie > .env
  echo GOOGLE_PLACES_API_KEY=vul_je_api_sleutel_in >> .env
  echo AIRTABLE_ACCESS_TOKEN=vul_je_airtable_token_in >> .env
  echo AIRTABLE_BASE_ID=vul_je_airtable_base_id_in >> .env
  echo AIRTABLE_TABLE_NAME=Places >> .env
  echo.
  echo BELANGRIJK: Je moet het .env bestand bewerken en je API sleutels invullen!
  echo.
  pause
)

REM Start de applicatie
node src/cli.js

echo.
echo Druk op een toets om af te sluiten...
pause > nul