require('dotenv').config();
const googleSearchService = require('../src/services/googleSearchService');
const openaiService = require('../src/services/openaiService');
const logger = require('../src/services/logger');

async function searchProfiles() {
  try {
    // Voorbeeld zoekparameters
    const searchParams = {
      function: 'Marketing Manager, Marketing Directeur',
      location: 'Amsterdam',
      company: 'Transfirm' // Optioneel
    };

    logger.info('Start profiel zoekopdracht', { searchParams });

    // Voer zoekopdracht uit
    const results = await googleSearchService.searchProfiles(searchParams);
    
    // Toon resultaten
    logger.info(`Gevonden ${results.length} profielen`);
    
    for (const result of results) {
      console.log('\n--- Profiel ---');
      console.log(`Naam: ${result.personName}`);
      console.log(`Functie: ${result.currentRole}`);
      console.log(`Bedrijf: ${result.companyName}`);
      console.log(`Locatie: ${result.location || 'Onbekend'}`);
      console.log(`LinkedIn: ${result.link}`);
      
      if (result.companyUrl) {
        console.log(`Website: ${result.companyUrl}`);
      }

      if (result.enrichedData) {
        console.log('\nVerrijkte data:');
        console.log(JSON.stringify(result.enrichedData, null, 2));
      }
    }

    // Toon statistieken
    const openaiStats = openaiService.getStats();
    console.log('\n--- OpenAI Statistieken ---');
    console.log(`Totaal requests: ${openaiStats.totalRequests}`);
    console.log(`Totaal tokens: ${openaiStats.totalTokens}`);
    console.log(`Gemiddeld tokens per request: ${openaiStats.averageTokensPerRequest}`);
    console.log(`Totaal fouten: ${openaiStats.totalErrors}`);

  } catch (error) {
    logger.error('Fout bij zoeken profielen:', error);
    process.exit(1);
  }
}

// Voer het voorbeeld uit
searchProfiles(); 