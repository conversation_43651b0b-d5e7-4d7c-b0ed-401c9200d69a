require('dotenv').config();
const axios = require('axios');

async function testWhopConfig() {
  const licenseKey = process.argv[2];
  const apiKey = process.env.WHOP_API_KEY;

  console.log('Testing Whop API configuratie...\n');
  
  try {
    // Gebruik het correcte validate_license endpoint
    const response = await axios.post(
      `https://api.whop.com/api/v2/memberships/${licenseKey}/validate_license`,
      {
        metadata: {
          machine_id: require('node-machine-id').machineIdSync(),
          hostname: require('os').hostname(),
          timestamp: new Date().toISOString()
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ API Response:');
    console.log(JSON.stringify(response.data, null, 2));
    console.log('\nLicentie status:', response.data.valid ? '✅ Geldig' : '❌ Ongeldig');

  } catch (error) {
    if (error.response?.data?.error?.status === 401) {
      console.error('❌ API Key permissie fout!');
      console.error('\nControleer in de Whop Developer Portal of de API key deze permissies heeft:');
      console.error('- memberships:validate');
      console.error('\nStappen:');
      console.error('1. Log in op Whop');
      console.error('2. Ga naar Developer Settings');
      console.error('3. Vind je API key');
      console.error('4. Controleer/update de permissies');
    } else {
      console.error('Error:', error.response?.data || error.message);
    }
  }
}

testWhopConfig(); 