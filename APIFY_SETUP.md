# Apify Setup Instructies

## Stap 1: Apify API Token Verkrijgen

1. Ga naar [Apify Console](https://console.apify.com/)
2. <PERSON>ak een gratis account aan of log in
3. Ga naar Settings > Integrations
4. <PERSON><PERSON><PERSON> je API token
5. Voeg het toe aan je `.env` bestand:

```env
APIFY_API_TOKEN=**********************************************
APIFY_ACTOR_ID=2Mdma1N6Fd0y3QEjR
```

## Stap 2: CLI Gebruiken

Start de CLI:
```bash
node src/cli.js
```

<PERSON><PERSON> een van de nieuwe Apify opties:
- **"Zoeken op locatie (Apify - snel)"** - Alleen Apify data, geen Firecrawl
- **"Zoeken op locatie (Apify hybride - beperkte Firecrawl)"** - Apify + contactgegevens

## Stap 3: Configuratie

### Voor Snelle Modus:
- Locatie: bijv. "Alkmaar, Netherlands"
- Zoektermen: bijv. "schild<PERSON>, t<PERSON><PERSON>, dakdekker"
- Vertraging: 2000ms (aanbevolen)
- Max plaatsen: 50 per zoekterm
- Firecrawl: Nee

### Voor Hybride Modus:
- Locatie: bijv. "Alkmaar, Netherlands"  
- Zoektermen: bijv. "schilder, timmerman, dakdekker"
- Vertraging: 3000ms (aanbevolen)
- Max plaatsen: 50 per zoekterm
- Firecrawl: Ja

## Stap 4: Direct Scripts

### Snelle Apify Modus:
```bash
node src/apify-alkmaar-scraping.js
```

### Hybride Apify Modus:
```bash
node src/apify-alkmaar-hybrid-scraping.js
```

### Tests:
```bash
# Test snelle modus
node src/test-apify.js

# Test hybride modus
node src/test-apify-hybrid.js
```

## Verwachte Output

### Snelle Modus:
- Alleen Apify data
- `scrape_source: 'apify_v1'`
- Bedrijfsnaam, adres, telefoon, website
- Extra info in notes (rating, categorie, beschrijving)

### Hybride Modus:
- Apify data + Firecrawl verrijking
- Contactpersoon naam en functie
- Email adres
- Bedrijfssamenvatting
- Alle bovenstaande velden

## Troubleshooting

### "APIFY_API_TOKEN ontbreekt"
- Controleer of de token correct is ingesteld in `.env`
- Herstart de applicatie na het toevoegen van de token

### "Rate limit errors"
- Verhoog de vertraging tussen requests
- Verlaag het aantal plaatsen per zoekterm

### "Geen resultaten"
- Controleer de locatie spelling
- Probeer bredere zoektermen
- Verifieer dat de Apify Actor ID correct is

## Kosten

- Apify: ~$0.001 per plaats
- 1000 plaatsen = ~$1
- Veel goedkoper dan Google Places API ($17 per 1000 requests)

## Support

Voor vragen of problemen, check:
1. `docs/apify-integration.md` - Uitgebreide documentatie
2. `docs/backend-processes.md` - Technische details
3. `.env.example` - Configuratie voorbeeld
