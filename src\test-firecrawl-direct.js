require('dotenv').config();
const axios = require('axios');

async function testFirecrawlDirect() {
  console.log('🔥 Direct Firecrawl API Test\n');

  const apiKey = process.env.FIRE_CRAWL_API_KEY;
  const baseUrl = 'https://api.firecrawl.dev/v1';
  
  console.log(`API Key: ${apiKey ? apiKey.substring(0, 5) + '...' + apiKey.substring(apiKey.length - 4) : 'NIET GEVONDEN'}`);
  console.log(`Base URL: ${baseUrl}\n`);

  if (!apiKey) {
    console.error('❌ FIRE_CRAWL_API_KEY niet gevonden in .env bestand');
    return;
  }

  // Test 1: Check account status/credits
  console.log('1. 📊 Test account status...');
  try {
    const accountResponse = await axios.get(`${baseUrl}/account`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('✅ Account status:', JSON.stringify(accountResponse.data, null, 2));
  } catch (error) {
    console.log('❌ Account status fout:', error.response?.status, error.response?.data || error.message);
  }

  // Test 2: Simple scrape (niet extract)
  console.log('\n2. 🕷️ Test simple scrape...');
  try {
    const scrapeResponse = await axios.post(`${baseUrl}/scrape`, {
      url: 'https://example.com',
      formats: ['markdown']
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('✅ Scrape response status:', scrapeResponse.status);
    console.log('✅ Scrape data keys:', Object.keys(scrapeResponse.data));
  } catch (error) {
    console.log('❌ Scrape fout:', error.response?.status, error.response?.data || error.message);
  }

  // Test 3: Extract (zoals in de applicatie)
  console.log('\n3. 🎯 Test extract (zoals in applicatie)...');
  try {
    const extractResponse = await axios.post(`${baseUrl}/extract`, {
      urls: ['https://example.com/*'],
      prompt: 'Extract contact information from this website',
      schema: {
        type: 'object',
        properties: {
          contact_person: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              email: { type: 'string' }
            }
          }
        }
      }
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    console.log('✅ Extract response status:', extractResponse.status);
    console.log('✅ Extract job ID:', extractResponse.data?.id);
    
    if (extractResponse.data?.id) {
      console.log('\n4. ⏳ Polling extract status...');
      let attempts = 0;
      const maxAttempts = 5;
      
      while (attempts < maxAttempts) {
        try {
          const statusResponse = await axios.get(`${baseUrl}/extract/${extractResponse.data.id}`, {
            headers: {
              'Authorization': `Bearer ${apiKey}`
            }
          });
          
          console.log(`   Poging ${attempts + 1}: Status = ${statusResponse.data.status}`);
          
          if (statusResponse.data.status === 'completed') {
            console.log('✅ Extract voltooid:', JSON.stringify(statusResponse.data.data, null, 2));
            break;
          } else if (statusResponse.data.status === 'failed') {
            console.log('❌ Extract mislukt:', statusResponse.data);
            break;
          }
          
          await new Promise(resolve => setTimeout(resolve, 10000)); // 10 seconden wachten
          attempts++;
        } catch (statusError) {
          console.log('❌ Status polling fout:', statusError.response?.status, statusError.response?.data || statusError.message);
          break;
        }
      }
    }
    
  } catch (error) {
    console.log('❌ Extract fout:', error.response?.status, error.response?.data || error.message);
    
    // Specifieke analyse van 402 error
    if (error.response?.status === 402) {
      console.log('\n🚨 HTTP 402 Payment Required Analysis:');
      console.log('   - Dit betekent meestal dat je API credits zijn uitgeput');
      console.log('   - Of dat je plan geen toegang heeft tot deze functie');
      console.log('   - Check je Firecrawl dashboard voor credit status');
      console.log('   - Mogelijk is extract een premium feature');
    }
  }

  // Test 4: Check wat voor plan/features beschikbaar zijn
  console.log('\n5. 🔍 Test beschikbare endpoints...');
  const endpoints = [
    '/scrape',
    '/crawl', 
    '/extract',
    '/search'
  ];
  
  for (const endpoint of endpoints) {
    try {
      // Probeer een OPTIONS request om te zien welke endpoints bestaan
      const response = await axios.options(`${baseUrl}${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      });
      console.log(`✅ ${endpoint}: beschikbaar (${response.status})`);
    } catch (error) {
      if (error.response?.status === 405) {
        console.log(`✅ ${endpoint}: bestaat maar OPTIONS niet ondersteund`);
      } else if (error.response?.status === 402) {
        console.log(`⚠️ ${endpoint}: bestaat maar vereist betaling`);
      } else if (error.response?.status === 404) {
        console.log(`❌ ${endpoint}: niet gevonden`);
      } else {
        console.log(`❓ ${endpoint}: onbekende status (${error.response?.status})`);
      }
    }
  }
}

// Voer test uit
if (require.main === module) {
  testFirecrawlDirect().catch(console.error);
}

module.exports = testFirecrawlDirect;
