# Places API Scraper

## Installatie
1. Download en pak het ZIP bestand uit
2. Ko<PERSON>er `.env.template` naar `.env`
3. Vul je API sleutels in in het `.env` bestand:
   - Google Places API key voor het ophalen van locaties
   - Airtable gegevens voor het opslaan van resultaten

## Eerste gebruik
1. Start de applicatie met `places-scraper.exe`
2. Voer je licentie key in (ontvangen na aankoop)
3. <PERSON><PERSON> een gebied om te scrapen of maak een nieuw grid
4. De resultaten worden opgeslagen in de `data` map en/of Airtable

## Support
Bij problemen:
- Email: <EMAIL>
- Website: www.jouwbedrijf.com/support 