/**
 * Debug versie van de test script voor de Places API Scraper met Firecrawl integratie
 */

require('dotenv').config();
const PlacesScraper = require('./scraper');
const placesApiClient = require('./services/placesApiClient');
const firecrawlClient = require('./services/firecrawlClient');
const Airtable = require('airtable');

// Airtable configuratie
const airtableToken = process.env.AIRTABLE_ACCESS_TOKEN;
const airtableBaseId = process.env.AIRTABLE_BASE_ID;
const airtableTableName = process.env.AIRTABLE_TABLE_NAME || 'Places';
const airtableViewName = process.env.AIRTABLE_VIEW_NAME || 'Grid view';

// Initialiseer Airtable
const base = new Airtable({ apiKey: airtableToken }).base(airtableBaseId);

async function testScraperWithFirecrawlDebug() {
  try {
    console.log('Debug test van Places API Scraper met Firecrawl integratie\n');

    // Test direct de Firecrawl integratie
    console.log('Test Firecrawl API direct...');
    const testWebsite = 'https://martianlogic.com/*';
    console.log(`Verwerken van website: ${testWebsite}`);
    
    const enrichedData = await firecrawlClient.enrichCompanyData(testWebsite);
    console.log('\nFirecrawl resultaten:');
    console.log(JSON.stringify(enrichedData, null, 2));
    
    // Controleer Airtable velden
    console.log('\nControleren van Airtable velden...');
    try {
      const airtableFields = await getAirtableFields();
      console.log('Beschikbare Airtable velden:');
      airtableFields.forEach(field => {
        console.log(`- ${field.name} (${field.type})`);
      });
      
      // Controleer of alle benodigde velden bestaan
      const requiredFields = [
        'Name', 'Place ID', 'Address', 'Latitude', 'Longitude',
        'Rating', 'Total Ratings', 'Search Term', 'Phone', 'Website',
        'Search Page URL', 'Contact Person', 'Contact Title', 
        'Contact Department', 'Contact Email', 'Contact Phone',
        'Company Summary', 'Has VMware', 'Has Cloud Computing', 
        'Has Cloud Solutions', 'Has Virtualization'
      ];
      
      const missingFields = requiredFields.filter(field => 
        !airtableFields.some(f => f.name === field)
      );
      
      if (missingFields.length > 0) {
        console.log('\n⚠️ Ontbrekende velden in Airtable:');
        missingFields.forEach(field => console.log(`- ${field}`));
        console.log('\nVoeg deze velden toe aan je Airtable tabel om de verrijkte data op te slaan.');
      } else {
        console.log('\n✅ Alle benodigde velden zijn aanwezig in Airtable.');
      }
      
      // Test direct opslaan in Airtable
      console.log('\nTest direct opslaan in Airtable...');
      const testFields = {
        'Name': 'Test VMware',
        'Place ID': 'test_place_id_' + Date.now(), // Unieke ID om duplicaten te voorkomen
        'Address': 'Test Address',
        'Latitude': 0,
        'Longitude': 0,
        'Rating': 4.5,
        'Total Ratings': 100,
        'Search Term': 'test',
        'Phone': '+1234567890',
        'Website': 'https://www.vmware.com',
        'Search Page URL': 'https://www.google.com/maps/search/?api=1&query=Test+VMware&query_place_id=test_place_id'
      };
      
      // Voeg verrijkte data toe - controleer eerst of enrichedData bestaat
      if (enrichedData) {
        if (enrichedData.contactPerson) {
          if (enrichedData.contactPerson.name) {
            testFields['Contact Person'] = enrichedData.contactPerson.name;
          }
          if (enrichedData.contactPerson.title) {
            testFields['Contact Title'] = enrichedData.contactPerson.title;
          }
          if (enrichedData.contactPerson.department) {
            testFields['Contact Department'] = enrichedData.contactPerson.department;
          }
          if (enrichedData.contactPerson.email) {
            testFields['Contact Email'] = enrichedData.contactPerson.email;
          }
          if (enrichedData.contactPerson.phone) {
            testFields['Contact Phone'] = enrichedData.contactPerson.phone;
          }
        }
        
        if (enrichedData.companySummary) {
          testFields['Company Summary'] = enrichedData.companySummary;
        }
        
        if (enrichedData.services) {
          testFields['Has VMware'] = enrichedData.services.hasVMware || false;
          testFields['Has Cloud Computing'] = enrichedData.services.hasCloudComputing || false;
          testFields['Has Cloud Solutions'] = enrichedData.services.hasCloudSolutions || false;
          testFields['Has Virtualization'] = enrichedData.services.hasVirtualization || false;
        }
      } else {
        // Voeg dummy data toe als er geen verrijkte data is
        testFields['Company Summary'] = 'Dit is een test bedrijfssamenvatting zonder verrijkte data.';
        testFields['Has VMware'] = false;
        testFields['Has Cloud Computing'] = true;
        testFields['Has Cloud Solutions'] = true;
        testFields['Has Virtualization'] = false;
      }
      
      console.log('Airtable velden met verrijkte data:');
      console.log(JSON.stringify(testFields, null, 2));
      
      // Maak record aan in Airtable
      const record = await base(airtableTableName).create(testFields, { typecast: true });
      console.log(`\n✅ Test record aangemaakt in Airtable met ID: ${record.id}`);
      
    } catch (airtableError) {
      console.error('\n❌ Fout bij Airtable operaties:', airtableError.message);
      if (airtableError.statusCode) {
        console.error(`Airtable status code: ${airtableError.statusCode}`);
      }
      if (airtableError.error) {
        console.error('Airtable error details:', airtableError.error);
      }
    }
    
    console.log('\n✅ Debug test voltooid!');
  } catch (error) {
    console.error('\n❌ Fout tijdens debug test:', error.message);
    console.error(error.stack);
  }
}

// Hulpfunctie om Airtable velden op te halen
async function getAirtableFields() {
  try {
    // Gebruik de werkende methode uit test-airtable.js
    const records = await base(airtableTableName).select({
      maxRecords: 1,
      view: airtableViewName
    }).firstPage();
    
    if (records.length > 0) {
      // Haal veldnamen op uit het eerste record
      const fieldNames = Object.keys(records[0].fields);
      return fieldNames.map(name => ({ name, type: 'unknown' }));
    }
    return [];
  } catch (error) {
    console.error('Fout bij ophalen Airtable velden:', error.message);
    return [];
  }
}

// Voer de debug test uit
testScraperWithFirecrawlDebug(); 