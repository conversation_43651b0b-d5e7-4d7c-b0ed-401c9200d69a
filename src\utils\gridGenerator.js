/**
 * Grid Generator
 * Genereert een grid van coördinaten rond een centrale locatie
 */
const axios = require('axios');
require('dotenv').config();
const fs = require('fs').promises;
const path = require('path');

class GridGenerator {
  constructor() {
    this.apiKey = process.env.GOOGLE_PLACES_API_KEY;
    if (!this.apiKey) {
      throw new Error('GOOGLE_PLACES_API_KEY is niet ingesteld in .env bestand');
    }
  }

  /**
   * Genereert een grid van coördinaten rond een centrale locatie
   * @param {Object} center - Centrale locatie {lat, lng} of naam van locatie (string)
   * @param {Object} options - Opties voor grid generatie
   * @returns {Array} Grid van coördinaten
   */
  async generateGrid(center, options = {}) {
    console.log('\n=== Grid Generatie Start ===');
    console.log('Configuratie:');
    
    // Standaard opties met logging
    const config = {
      gridSize: options.gridSize || 3,
      cellSize: options.cellSize || 500,
      radius: options.radius || 500,
      overlap: options.overlap || 0.2,
      ...options
    };
    
    console.log(`- Grid grootte: ${config.gridSize}x${config.gridSize}`);
    console.log(`- Cel grootte: ${config.cellSize}m`);
    console.log(`- Zoekradius: ${config.radius}m`);
    console.log(`- Overlap: ${config.overlap * 100}%`);

    // Bepaal de centrale coördinaten
    let centerCoords;
    
    if (options.centerLat !== undefined && options.centerLng !== undefined) {
      centerCoords = {
        lat: options.centerLat,
        lng: options.centerLng
      };
      console.log('\nGebruik handmatige coördinaten:');
      console.log(`- Breedtegraad: ${centerCoords.lat}`);
      console.log(`- Lengtegraad: ${centerCoords.lng}`);
    } 
    else if (center && typeof center === 'object' && center.lat && center.lng) {
      centerCoords = center;
      console.log('\nGebruik object coördinaten:');
      console.log(`- Breedtegraad: ${centerCoords.lat}`);
      console.log(`- Lengtegraad: ${centerCoords.lng}`);
    } 
    else if (typeof center === 'string') {
      try {
        console.log(`\nLocatie "${center}" omzetten naar coördinaten...`);
        centerCoords = await this.geocodeLocation(center);
        console.log('Geocoding succesvol:');
        console.log(`- Breedtegraad: ${centerCoords.lat}`);
        console.log(`- Lengtegraad: ${centerCoords.lng}`);
      } catch (error) {
        console.error('\nFout bij geocoding:');
        console.error(`- ${error.message}`);
        throw error;
      }
    } else {
      throw new Error('Ongeldige invoer voor grid generatie');
    }

    // Valideer coördinaten
    if (!centerCoords || typeof centerCoords.lat !== 'number' || typeof centerCoords.lng !== 'number') {
      throw new Error('Ongeldige coördinaten voor grid generatie');
    }

    console.log('\nGrid berekenen...');
    
    // Bepaal afstand tussen gridpunten
    const effectiveCellSize = config.cellSize * (1 - config.overlap);
    console.log(`- Effectieve cel grootte: ${effectiveCellSize}m (met overlap)`);

    // Bereken grid punten
    const halfSize = Math.floor(config.gridSize / 2);
    const grid = [];
    
    // Conversie factoren
    const metersPerDegreeLatitude = 111320;
    const metersPerDegreeLongitude = 111320 * Math.cos(centerCoords.lat * (Math.PI / 180));
    
    console.log(`- Conversie factor lengtegraad: ${metersPerDegreeLongitude.toFixed(2)}m per graad`);
    
    // Genereer het grid
    for (let i = -halfSize; i <= halfSize; i++) {
      for (let j = -halfSize; j <= halfSize; j++) {
        const latOffset = (i * effectiveCellSize) / metersPerDegreeLatitude;
        const lngOffset = (j * effectiveCellSize) / metersPerDegreeLongitude;
        
        grid.push({
          lat: centerCoords.lat + latOffset,
          lng: centerCoords.lng + lngOffset,
          radius: config.radius
        });
      }
    }
    
    console.log(`\nGrid gegenereerd met ${grid.length} punten`);
    console.log('Voorbeeld gridpunten:');
    grid.slice(0, 3).forEach((point, index) => {
      console.log(`- Punt ${index + 1}: ${point.lat.toFixed(6)}, ${point.lng.toFixed(6)} (r=${point.radius}m)`);
    });
    console.log('...');
    
    console.log('\n=== Grid Generatie Voltooid ===\n');
    
    return grid;
  }

  /**
   * Converteert een locatienaam naar coördinaten
   * @param {string} location - Naam van de locatie
   * @returns {Object} Coördinaten {lat, lng}
   */
  async geocodeLocation(location) {
    try {
      console.log(`Google Geocoding API aanroepen voor locatie "${location}"...`);
      const response = await axios.get(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
          location
        )}&key=${this.apiKey}`
      );

      if (response.data.status === 'REQUEST_DENIED') {
        const errorMsg = response.data.error_message || 'Geen details beschikbaar';
        console.error(`Google Geocoding API geweigerd: ${errorMsg}`);
        console.error('\nMogelijke oorzaken:');
        console.error('1. API sleutel heeft geen toegang tot Geocoding API');
        console.error('2. API sleutel heeft een factureringsaccount nodig');
        console.error('3. De Geocoding API is niet geactiveerd in de Google Cloud Console');
        console.error('\nOplossing:');
        console.error('1. Ga naar https://console.cloud.google.com/');
        console.error('2. Selecteer je project');
        console.error('3. Ga naar "APIs & Services" > "Library"');
        console.error('4. Zoek naar "Geocoding API" en activeer deze');
        console.error('5. Controleer of je een actieve factureringsaccount hebt gekoppeld\n');
        throw new Error(`Geocoding API toegang geweigerd: ${errorMsg}`);
      }

      if (response.data.status !== 'OK') {
        throw new Error(`Geocoding fout: ${response.data.status}`);
      }

      if (response.data.results.length === 0) {
        throw new Error('Geen resultaten gevonden voor deze locatie');
      }

      return response.data.results[0].geometry.location;
    } catch (error) {
      if (error.response) {
        console.error('API response error:', error.response.data);
      }
      throw error.message ? error : new Error(error.toString());
    }
  }

  /**
   * Slaat een gegenereerd grid op in een configuratiebestand
   * @param {string} location - Naam van de locatie
   * @param {Array} grid - Grid van coördinaten
   * @param {Array} keywords - Zoektermen
   * @param {string} configPath - Bestandsnaam voor configuratie
   * @returns {string} Pad naar opgeslagen configuratiebestand
   */
  async saveGridConfig(location, grid, keywords, configPath) {
    try {
      // Maak het configuratie object
      const config = {
        name: location,
        gridCoordinates: grid,
        keywords: keywords || []
      };
      
      // Zorg dat de directory bestaat
      await fs.mkdir(path.dirname(configPath), { recursive: true });
      
      // Schrijf de configuratie
      await fs.writeFile(configPath, JSON.stringify(config, null, 2));
      
      return configPath;
    } catch (error) {
      throw new Error(`Fout bij opslaan grid configuratie: ${error.message}`);
    }
  }
}

module.exports = { GridGenerator };