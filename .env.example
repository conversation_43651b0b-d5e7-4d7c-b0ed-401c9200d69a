# Google Places API Configuration
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here

# Airtable Configuration
AIRTABLE_ACCESS_TOKEN=your_airtable_access_token_here
AIRT<PERSON>LE_BASE_ID=your_airtable_base_id_here
AIRTABLE_TABLE_NAME=Places

# Firecrawl Configuration
FIRE_CRAWL_API_KEY=your_firecrawl_api_key_here

# Apify Configuration (NEW)
APIFY_API_TOKEN=your_apify_api_token_here
APIFY_ACTOR_ID=2Mdma1N6Fd0y3QEjR

# License Configuration
WHOP_API_KEY=your_whop_api_key_here

# Optional: Logging Configuration
LOG_LEVEL=info
