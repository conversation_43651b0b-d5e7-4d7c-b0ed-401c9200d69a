require('dotenv').config();
const Airtable = require('airtable');
const logger = require('./logger');

const accessToken = process.env.AIRTABLE_ACCESS_TOKEN;
const baseId = process.env.AIRTABLE_BASE_ID;
const tableName = process.env.AIRTABLE_TABLE_NAME || 'Places';

if (!accessToken || !baseId) {
  logger.error('AIRTABLE_ACCESS_TOKEN en AIRTABLE_BASE_ID moeten worden ingesteld in het .env bestand');
  throw new Error('Airtable configuratie ontbreekt');
}

const base = new Airtable({ apiKey: accessToken }).base(baseId);

/**
 * Mapt Places API data naar het Airtable schema
 * @param {Object} placeDetails - Places API details
 * @param {Object} enrichedData - Firecrawl verrijkte data (optioneel)
 * @param {string} website - Website URL (optioneel)
 * @returns {Object} - Gemapte velden voor Airtable
 */
function mapPlacesToAirtableFields(placeDetails, enrichedData = null, website = null) {
  const fields = {
    'scrape_source': 'crawler_v1', // Gebruik bestaande waarde uit Airtable
    'scrape_timestamp_iso': new Date().toISOString()
  };

  // Basis informatie van Places API
  if (placeDetails) {
    if (placeDetails.name) fields['company_name'] = placeDetails.name;
    if (placeDetails.formatted_phone_number) fields['phone'] = placeDetails.formatted_phone_number;
    if (placeDetails.website) fields['website'] = placeDetails.website;
    if (placeDetails.url) fields['searchpage_url'] = placeDetails.url;

    // Email alleen gebruiken als het daadwerkelijk beschikbaar is in Places API
    // NIET schatten van website - alleen echte email adressen gebruiken
    if (placeDetails.formatted_address) {
      // Probeer adres te splitsen - probeer address_street als tekst
      const addressParts = placeDetails.formatted_address.split(',').map(part => part.trim());

      // Voeg straat toe aan address_street veld (nu tekst type)
      if (addressParts.length >= 1) {
        fields['address_street'] = addressParts[0];
      }

      if (addressParts.length >= 2) fields['address_city'] = addressParts[1];
      if (addressParts.length >= 3) fields['address_postcode'] = addressParts[2];
      if (addressParts.length >= 4) {
        // Map land naar bestaande Airtable waarden
        const country = addressParts[3].toLowerCase();
        if (country.includes('nederland') || country.includes('netherlands')) {
          fields['address_country'] = 'NL';
        } else {
          fields['address_country'] = 'NL'; // Default naar NL
        }
      }
    }
  }

  // Website als fallback
  if (website && !fields['website']) {
    fields['website'] = website;
  }

  // Verrijkte data van Firecrawl
  if (enrichedData) {
    if (enrichedData.contactPerson) {
      if (enrichedData.contactPerson.name) fields['decision_maker_name'] = enrichedData.contactPerson.name;
      if (enrichedData.contactPerson.title) fields['role_title'] = enrichedData.contactPerson.title;
      if (enrichedData.contactPerson.email) fields['email'] = enrichedData.contactPerson.email;
      if (enrichedData.contactPerson.phone && !fields['phone']) fields['phone'] = enrichedData.contactPerson.phone;
    }

    if (enrichedData.companySummary) {
      fields['notes'] = enrichedData.companySummary.substring(0, 1000);
    }
  }

  return fields;
}

/**
 * Schrijft een profiel weg naar Airtable
 * @param {Object} profile - Profiel met verrijkte data
 * @returns {Promise<string|null>} - Record ID of null
 */
async function addProfileToAirtable(profile) {
  // Bepaal e-mail en telefoon (contactpersoon heeft voorrang)
  let email = '';
  let phone = '';
  let address = '';
  if (profile.enrichedData) {
    if (profile.enrichedData.contact_person && (profile.enrichedData.contact_person.email || profile.enrichedData.contact_person.phone)) {
      email = profile.enrichedData.contact_person.email || '';
      phone = profile.enrichedData.contact_person.phone || '';
    } else if (profile.enrichedData.company_contact) {
      email = profile.enrichedData.company_contact.email || '';
      phone = profile.enrichedData.company_contact.phone || '';
      address = profile.enrichedData.company_contact.address || '';
    }
  }

  // Map naar het bestaande Airtable schema
  const fields = {
    'company_name': profile.company || '',
    'decision_maker_name': profile.name || '',
    'role_title': profile.currentRole || '',
    'website': profile.website || '',
    'scrape_source': 'crawler_v1',
    'phone': phone,
    'email': email,
    'scrape_timestamp_iso': new Date().toISOString(),
    'notes': profile.enrichedData?.summary || ''
  };

  // Voeg adres toe als het beschikbaar is - skip address_street vanwege datum veld probleem
  if (address) {
    // Probeer adres te splitsen in componenten
    const addressParts = address.split(',').map(part => part.trim());
    // Skip address_street vanwege Airtable datum veld probleem
    if (addressParts.length >= 2) fields['address_city'] = addressParts[1];
    if (addressParts.length >= 3) fields['address_postcode'] = addressParts[2];
    if (addressParts.length >= 4) {
      // Map land naar bestaande Airtable waarden
      const country = addressParts[3].toLowerCase();
      if (country.includes('nederland') || country.includes('netherlands')) {
        fields['address_country'] = 'NL';
      } else {
        fields['address_country'] = 'NL'; // Default naar NL
      }
    }

    // Voeg volledig adres toe aan notes
    fields['notes'] = fields['notes'] ?
      `${fields['notes']} | Adres: ${address}` :
      `Adres: ${address}`;
  }

  logger.info('Schrijf profiel weg naar Airtable', fields);

  try {
    const createdRecord = await base(tableName).create(fields);
    logger.info('Airtable record aangemaakt', { id: createdRecord.getId() });
    return createdRecord.getId();
  } catch (error) {
    logger.error('Fout bij wegschrijven naar Airtable', { error: error.message, fields });
    return null;
  }
}

/**
 * Probeert een email adres af te leiden van een website URL
 * @param {string} website - Website URL
 * @param {string} companyName - Bedrijfsnaam
 * @returns {string|null} - Geschat email adres of null
 */
function estimateEmailFromWebsite(website, companyName) {
  if (!website || !companyName) return null;

  try {
    const url = new URL(website);
    const domain = url.hostname.replace('www.', '');
    const cleanCompanyName = companyName.toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .substring(0, 10); // Eerste 10 karakters

    // Veelvoorkomende email patronen
    const patterns = [
      `info@${domain}`,
      `contact@${domain}`,
      `hello@${domain}`,
      `mail@${domain}`,
      `${cleanCompanyName}@${domain}`
    ];

    // Retourneer het meest waarschijnlijke patroon
    return patterns[0]; // info@ is het meest voorkomend
  } catch (error) {
    return null;
  }
}

/**
 * Mapt alleen Places API data naar Airtable (zonder Firecrawl verrijking)
 * @param {Object} placeDetails - Places API details
 * @returns {Object} - Gemapte velden voor Airtable
 */
function mapPlacesOnlyToAirtableFields(placeDetails) {
  const fields = {
    'scrape_source': 'crawler_v1',
    'scrape_timestamp_iso': new Date().toISOString()
  };

  if (placeDetails) {
    // Basis informatie
    if (placeDetails.name) fields['company_name'] = placeDetails.name;
    if (placeDetails.formatted_phone_number) fields['phone'] = placeDetails.formatted_phone_number;
    if (placeDetails.website) fields['website'] = placeDetails.website;
    if (placeDetails.url) fields['searchpage_url'] = placeDetails.url;

    // Email alleen gebruiken als het daadwerkelijk beschikbaar is in Places API
    // NIET schatten van website - alleen echte email adressen gebruiken

    // Adres verwerking - probeer address_street als tekst
    if (placeDetails.formatted_address) {
      const addressParts = placeDetails.formatted_address.split(',').map(part => part.trim());

      // Voeg straat toe aan address_street veld (nu tekst type)
      if (addressParts.length >= 1) {
        fields['address_street'] = addressParts[0];
      }

      if (addressParts.length >= 2) fields['address_city'] = addressParts[1];
      if (addressParts.length >= 3) fields['address_postcode'] = addressParts[2];
      if (addressParts.length >= 4) {
        const country = addressParts[3].toLowerCase();
        if (country.includes('nederland') || country.includes('netherlands')) {
          fields['address_country'] = 'NL';
        } else {
          fields['address_country'] = 'NL'; // Default naar NL
        }
      }
    }

    // Extra Places API informatie als notities
    const notes = [];
    if (placeDetails.rating) notes.push(`Rating: ${placeDetails.rating}/5`);
    if (placeDetails.user_ratings_total) notes.push(`${placeDetails.user_ratings_total} reviews`);
    if (placeDetails.types && placeDetails.types.length > 0) {
      notes.push(`Types: ${placeDetails.types.join(', ')}`);
    }
    if (notes.length > 0) {
      fields['notes'] = notes.join(' | ');
    }
  }

  return fields;
}

/**
 * Mapt Places API data + beperkte Firecrawl data naar Airtable (hybride modus)
 * Gebruikt Firecrawl alleen voor: decision_maker_name, role_title, email, summary
 * @param {Object} placeDetails - Places API details
 * @param {Object} enrichedData - Firecrawl verrijkte data (beperkt)
 * @returns {Object} - Gemapte velden voor Airtable
 */
function mapPlacesWithLimitedFirecrawlToAirtableFields(placeDetails, enrichedData = null) {
  // Start met de basis Places API mapping
  const fields = mapPlacesOnlyToAirtableFields(placeDetails);

  // Voeg beperkte Firecrawl data toe als beschikbaar
  if (enrichedData) {
    // decision_maker_name - contactpersoon naam
    if (enrichedData.contact_person?.name) {
      fields['decision_maker_name'] = enrichedData.contact_person.name;
    }

    // role_title - functietitel van contactpersoon
    if (enrichedData.contact_person?.role) {
      fields['role_title'] = enrichedData.contact_person.role;
    }

    // email - prioriteit: contactpersoon email, anders bedrijf email
    if (enrichedData.contact_person?.email) {
      fields['email'] = enrichedData.contact_person.email;
    } else if (enrichedData.company_contact?.email) {
      fields['email'] = enrichedData.company_contact.email;
    }

    // summary - bedrijfssamenvatting
    if (enrichedData.companySummary) {
      // Voeg summary toe aan bestaande notes of vervang ze
      const existingNotes = fields['notes'] || '';
      const summary = `Samenvatting: ${enrichedData.companySummary}`;

      if (existingNotes) {
        fields['notes'] = `${existingNotes} | ${summary}`;
      } else {
        fields['notes'] = summary;
      }

      // Ook als apart veld voor betere toegankelijkheid
      fields['summary'] = enrichedData.companySummary;
    }
  }

  return fields;
}

module.exports = {
  addProfileToAirtable,
  mapPlacesToAirtableFields,
  mapPlacesOnlyToAirtableFields,
  mapPlacesWithLimitedFirecrawlToAirtableFields
};