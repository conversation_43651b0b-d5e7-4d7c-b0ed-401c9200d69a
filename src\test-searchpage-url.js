require('dotenv').config();
const { mapPlacesOnlyToAirtableFields } = require('./services/airtableService');
const Airtable = require('airtable');

// Airtable configuratie
const accessToken = process.env.AIRTABLE_ACCESS_TOKEN;
const baseId = process.env.AIRTABLE_BASE_ID;
const tableName = process.env.AIRTABLE_TABLE_NAME || 'Places';

if (!accessToken || !baseId) {
  console.error('Fout: AIRTABLE_ACCESS_TOKEN en AIRTABLE_BASE_ID moeten worden ingesteld in het .env bestand');
  process.exit(1);
}

const base = new Airtable({ apiKey: accessToken }).base(baseId);

async function testSearchPageUrl() {
  console.log('🧪 Test searchpage_url mapping...\n');

  // Test data - simuleer Places API response met search page URL
  const mockPlaceDetails = {
    name: 'Test Dakdekker BV',
    formatted_address: 'Teststraat 123, Alkmaar, 1811 AB, Nederland',
    formatted_phone_number: '+31 72 123 4567',
    website: 'https://testdakdekker.nl',
    url: 'https://maps.google.com/?cid=12345678901234567890', // Google Search page URL
    place_id: 'ChIJtest123',
    rating: 4.5,
    user_ratings_total: 25,
    types: ['roofing_contractor', 'establishment']
  };

  // Test de mapping functie
  console.log('1. Test mapping functie...');
  const mappedFields = mapPlacesOnlyToAirtableFields(mockPlaceDetails);
  
  console.log('✅ Gemapte velden:');
  Object.entries(mappedFields).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });

  // Controleer of searchpage_url aanwezig is
  if (mappedFields.searchpage_url) {
    console.log('\n✅ searchpage_url is correct toegevoegd!');
    console.log(`   URL: ${mappedFields.searchpage_url}`);
  } else {
    console.log('\n❌ searchpage_url ontbreekt nog steeds');
  }

  // Test schrijven naar Airtable
  if (process.argv.includes('--write-test')) {
    console.log('\n2. Test schrijven naar Airtable...');
    try {
      const createdRecord = await base(tableName).create(mappedFields);
      console.log('✅ Test record succesvol aangemaakt!');
      console.log(`   Record ID: ${createdRecord.getId()}`);
      console.log('   Velden in Airtable:');
      Object.entries(createdRecord.fields).forEach(([key, value]) => {
        console.log(`   ${key}: ${value}`);
      });
      
      // Controleer specifiek de searchpage_url
      if (createdRecord.fields.searchpage_url) {
        console.log('\n🎉 searchpage_url succesvol opgeslagen in Airtable!');
      } else {
        console.log('\n⚠️ searchpage_url niet gevonden in opgeslagen record');
      }
      
    } catch (error) {
      console.error('❌ Fout bij schrijven naar Airtable:');
      console.error(`   ${error.message}`);
      if (error.statusCode) {
        console.error(`   Status code: ${error.statusCode}`);
        console.error(`   Details: ${JSON.stringify(error.error || {})}`);
      }
    }
  } else {
    console.log('\n💡 Gebruik --write-test om daadwerkelijk naar Airtable te schrijven');
  }
}

testSearchPageUrl().catch(console.error);
