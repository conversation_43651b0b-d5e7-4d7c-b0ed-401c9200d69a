#!/usr/bin/env node
/**
 * Interactieve CLI interface voor de Places API Scraper
 */
const dotenv = require('dotenv');
dotenv.config();

const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const readline = require('readline');
const licenseManager = require('./licenseInstance');
const inquirer = require('inquirer');
const placesApi = require('./index');
const PlacesScraper = require('./scraper');
const ApifyScraper = require('./apifyScraper'); // Nieuwe Apify scraper
const geocoder = require('./services/geocoder'); // Voor het omzetten van plaatsnamen naar coördinaten
const pkg = require('../package.json');
const { program } = require('commander');
const googleSearchService = require('./services/googleSearchService');

function getBasePath() {
  if (process.pkg === undefined) {
    return process.cwd();
  }
  return path.dirname(process.execPath);
}

const BASE_DIR = getBasePath();
const CONFIG_DIR = path.join(BASE_DIR, 'config');
const DATA_DIR = path.join(BASE_DIR, 'data');
const LOGS_DIR = path.join(BASE_DIR, 'logs');

// Functie om alle benodigde directories aan te maken
async function ensureAllDirectories() {
  await Promise.all([
    fs.mkdir(CONFIG_DIR, { recursive: true }),
    fs.mkdir(DATA_DIR, { recursive: true }),
    fs.mkdir(LOGS_DIR, { recursive: true })
  ]);
}

// Functie om directory te controleren en aan te maken indien nodig
async function ensureDirectoryExists(dir) {
  try {
    await fs.mkdir(dir, { recursive: true });
  } catch (err) {
    if (err.code !== 'EEXIST') throw err;
  }
}

// Functie om beschikbare configuratiebestanden te tonen
async function showConfigFiles() {
  try {
    await fs.mkdir(CONFIG_DIR, { recursive: true });
    
    const files = await fs.readdir(CONFIG_DIR);
    const configFiles = files.filter(file => file.endsWith('.json'));
    
    if (configFiles.length === 0) {
      console.log('\n❌ Geen configuraties gevonden in config/ map');
      return null;
    }
    
    return configFiles;
  } catch (error) {
    console.error('Fout bij lezen configuraties:', error);
    return null;
  }
}

// Gebruik path.join voor het laden van configuraties
async function loadConfig(configFile) {
  try {
    const configPath = path.join(CONFIG_DIR, configFile);
    console.log('Laden configuratie:', configPath);
    const configData = await fs.readFile(configPath, 'utf8');
    return JSON.parse(configData);
  } catch (error) {
    throw new Error(`Configuratie laden mislukt: ${error.message}`);
  }
}

// Helper functie voor licentie prompt
async function promptForLicense() {
  const { licenseKey } = await inquirer.prompt([{
    type: 'input',
    name: 'licenseKey',
    message: 'Voer je licentie key in:'
  }]);

  try {
    const isValid = await licenseManager.validateLicense(licenseKey);
    if (!isValid) {
      console.error('\n❌ Ongeldige licentie');
      console.log('Controleer of:');
      console.log('1. De licentie key correct is ingevoerd');
      console.log('2. De licentie actief is');
      console.log('3. De licentie niet al op een ander apparaat wordt gebruikt');
      await new Promise(resolve => setTimeout(resolve, 5000));
      process.exit(1);
    }
    console.log('\n✅ Licentie succesvol gevalideerd!');
    console.log('Even geduld terwijl het hoofdmenu wordt geladen...');
    await new Promise(resolve => setTimeout(resolve, 1000));
  } catch (error) {
    console.error('\n❌ Fout bij licentievalidatie:');
    console.error(error.message);
    if (error.response?.data) {
      console.log('\nDetails van Whop API:');
      console.log(JSON.stringify(error.response.data, null, 2));
    }
    await new Promise(resolve => setTimeout(resolve, 5000));
    process.exit(1);
  }
}

/**
 * Interactieve functie om een plaats om te zetten naar coördinaten
 * @returns {Promise<{lat: number, lng: number, name: string}>} Coördinaten en naam van de plaats
 */
async function promptForLocation() {
  const { location } = await inquirer.prompt([{
    type: 'input',
    name: 'location',
    message: 'Voer een plaatsnaam in (bijv. "Amsterdam"):',
    validate: input => input.trim() !== '' ? true : 'Plaatsnaam is verplicht'
  }]);

  try {
    console.log(`Zoeken naar coördinaten voor "${location}"...`);
    const coordinates = await geocoder.geocode(location);
    
    if (!coordinates) {
      throw new Error(`Geen coördinaten gevonden voor "${location}"`);
    }
    
    console.log(`✅ Coördinaten gevonden: ${coordinates.lat}, ${coordinates.lng}`);
    return {
      lat: coordinates.lat,
      lng: coordinates.lng,
      name: location
    };
  } catch (error) {
    console.error(`❌ Fout bij geocoding: ${error.message}`);
    return promptForLocation(); // Probeer opnieuw
  }
}

/**
 * Vraagt de gebruiker om een zoekradius
 * @returns {Promise<number>} De radius in kilometers
 */
async function promptForRadius() {
  const { radius } = await inquirer.prompt([{
    type: 'number',
    name: 'radius',
    message: 'Voer de zoekradius in kilometers in:',
    default: 5,
    validate: input => {
      const num = parseFloat(input);
      if (isNaN(num)) return 'Voer een geldig getal in';
      if (num <= 0) return 'De radius moet groter zijn dan 0';
      if (num > 50) return 'De radius mag niet groter zijn dan 50km';
      return true;
    }
  }]);
  
  return radius;
}

/**
 * Vraagt de gebruiker om zoektermen
 * @returns {Promise<string[]>} Array van zoektermen
 */
async function promptForKeywords() {
  const { keywordsInput } = await inquirer.prompt([{
    type: 'input',
    name: 'keywordsInput',
    message: 'Voer zoektermen in, gescheiden door komma\'s:',
    validate: input => input.trim() !== '' ? true : 'Minstens één zoekterm is verplicht'
  }]);
  
  return keywordsInput.split(',').map(k => k.trim()).filter(k => k !== '');
}

/**
 * Vraagt de gebruiker om scraper opties
 * @returns {Promise<Object>} Scraper opties
 */
async function promptForOptions() {
  const { gridSize, delay, enableEnrichment } = await inquirer.prompt([
    {
      type: 'number',
      name: 'gridSize',
      message: 'Grootte van elke grid-cel in meters:',
      default: 500,
      validate: input => {
        const num = parseInt(input);
        if (isNaN(num) || num <= 0) return 'Voer een positief getal in';
        return true;
      }
    },
    {
      type: 'number',
      name: 'delay',
      message: 'Vertraging tussen API-verzoeken in milliseconden:',
      default: 200,
      validate: input => {
        const num = parseInt(input);
        if (isNaN(num) || num < 100) return 'Minimaal 100ms vertraging is vereist';
        return true;
      }
    },
    {
      type: 'confirm',
      name: 'enableEnrichment',
      message: 'Website data verrijken met Firecrawl?',
      default: true
    }
  ]);
  
  return {
    gridSizeM: gridSize,
    delayBetweenRequests: delay,
    enableFirecrawlEnrichment: enableEnrichment
  };
}

/**
 * Vraagt de gebruiker om scraper opties voor Apify
 * @returns {Promise<Object>} Scraper opties
 */
async function promptForScraperOptions() {
  const { delay, maxPlaces, enableEnrichment } = await inquirer.prompt([
    {
      type: 'number',
      name: 'delay',
      message: 'Vertraging tussen API-verzoeken in milliseconden:',
      default: 2000,
      validate: input => {
        const num = parseInt(input);
        if (isNaN(num) || num < 1000) return 'Minimaal 1000ms vertraging is vereist voor Apify';
        return true;
      }
    },
    {
      type: 'number',
      name: 'maxPlaces',
      message: 'Maximum aantal plaatsen per zoekterm:',
      default: 50,
      validate: input => {
        const num = parseInt(input);
        if (isNaN(num) || num <= 0 || num > 500) return 'Voer een getal tussen 1 en 500 in';
        return true;
      }
    },
    {
      type: 'confirm',
      name: 'enableEnrichment',
      message: 'Website data verrijken met Firecrawl?',
      default: false
    }
  ]);

  return {
    delayBetweenRequests: delay,
    maxPlacesPerSearch: maxPlaces,
    enableFirecrawlEnrichment: enableEnrichment
  };
}

/**
 * Vraagt de gebruiker om een bestand met URL's te selecteren
 * @returns {Promise<string>} Pad naar het geselecteerde bestand
 */
async function promptForUrlFile() {
  const { filePath } = await inquirer.prompt([{
    type: 'input',
    name: 'filePath',
    message: 'Voer het pad naar het bestand met URL\'s in (txt of json):',
    validate: async (input) => {
      try {
        const stats = await fs.stat(input);
        if (!stats.isFile()) return 'Dit is geen bestand';
        
        if (!input.endsWith('.txt') && !input.endsWith('.json')) {
          return 'Bestand moet een .txt of .json extensie hebben';
        }
        
        return true;
      } catch (error) {
        return 'Bestand bestaat niet of is niet toegankelijk';
      }
    }
  }]);
  
  return filePath;
}

/**
 * Leest URL's uit een bestand
 * @param {string} filePath - Pad naar het bestand
 * @returns {Promise<string[]>} Array van URL's
 */
async function readUrlsFromFile(filePath) {
  try {
    const content = await fs.readFile(filePath, 'utf8');
    
    if (filePath.endsWith('.json')) {
      // Probeer als JSON te parsen
      try {
        const data = JSON.parse(content);
        
        // Check verschillende mogelijke formaten
        if (Array.isArray(data)) {
          // Als het een array is, neem aan dat het een array van strings of objecten is
          return data.map(item => typeof item === 'string' ? item : (item.url || item.website || '')).filter(url => url);
        } else if (typeof data === 'object') {
          // Als het een object is, zoek naar een urls of websites veld
          if (Array.isArray(data.urls)) return data.urls.filter(url => url);
          if (Array.isArray(data.websites)) return data.websites.filter(url => url);
          
          // Anders, verzamel alle string properties die op URL's lijken
          return Object.values(data)
            .filter(val => typeof val === 'string' && (val.startsWith('http') || val.includes('www.')));
        }
      } catch (error) {
        throw new Error(`Ongeldige JSON in bestand: ${error.message}`);
      }
    } else {
      // Behandel als tekstbestand met één URL per regel of komma-gescheiden
      // Eerst proberen als één URL per regel
      let urls = content.split('\n')
        .map(line => line.trim())
        .filter(line => line);
      
      // Als er maar één regel is, probeer komma-gescheiden
      if (urls.length === 1 && urls[0].includes(',')) {
        urls = urls[0].split(',')
          .map(url => url.trim())
          .filter(url => url);
      }
      
      return urls;
    }
    
    throw new Error('Geen geldige URL\'s gevonden in bestand');
  } catch (error) {
    throw new Error(`Fout bij lezen van bestand: ${error.message}`);
  }
}

/**
 * Verwerkt een lijst van URL's met de scraper
 * @param {string[]} urls - Array van URL's om te verwerken
 * @param {Object} options - Scraper opties
 */
async function processUrlList(urls, options) {
  console.log(`\n🔍 Verwerken van ${urls.length} URL's...\n`);
  
  // Configureer scraper
  const scraper = new PlacesScraper({
    delayBetweenRequests: options.delayBetweenRequests,
    enableFirecrawlEnrichment: options.enableFirecrawlEnrichment,
    firecrawlMode: options.firecrawlMode || 'full',
    cacheProcessedUrls: true, // Zet URL caching aan
    logLevel: 'info'
  });
  
  // Initialiseer de scraper
  await scraper.initialize();
  
  let processed = 0;
  let successful = 0;
  let failed = 0;
  let skipped = 0;
  
  for (let i = 0; i < urls.length; i++) {
    let url = urls[i];
    
    try {
      console.log(`\n[${processed + 1}/${urls.length}] Verwerken van URL: ${url}`);
      
      // Verwijder aanhalingstekens en komma's als die aanwezig zijn
      url = url.replace(/[",]/g, '').trim();
      
      // Controleer of de URL geldig is
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        console.log(`Toevoegen van https:// aan URL: ${url}`);
        url = 'https://' + url;
      }
      
      // Verwerk de URL met zowel Places API als Firecrawl
      const result = await scraper.processUrlWithPlacesApi(url);
      
      if (result) {
        if (result.skipped) {
          skipped++;
          console.log(`⏭️ Overgeslagen: ${url} (al eerder verwerkt)`);
        } else {
          successful++;
          
          // Toon een samenvatting van de gevonden gegevens
          console.log(`✅ Succesvol verwerkt: ${url}`);
          
          if (result.placeDetails) {
            console.log(`  📍 Places API: ${result.placeDetails.name}`);
            if (result.placeDetails.formatted_address) {
              console.log(`     Adres: ${result.placeDetails.formatted_address}`);
            }
          } else {
            console.log(`  ⚠️ Geen Places API gegevens gevonden`);
          }
          
          if (result.enrichedData) {
            console.log(`  🔍 Firecrawl: Data verrijkt`);
          } else {
            console.log(`  ⚠️ Geen Firecrawl verrijking`);
          }
        }
      } else {
        failed++;
        console.log(`⚠️ Geen data gevonden voor: ${url}`);
      }
    } catch (error) {
      failed++;
      console.error(`❌ Fout bij verwerken van ${url}: ${error.message}`);
    }
    
    processed++;
    
    // Toon voortgang
    console.log(`\nVoortgang: ${processed}/${urls.length} (${successful} succesvol, ${skipped} overgeslagen, ${failed} mislukt)`);
    
    // Wacht even tussen verzoeken
    if (processed < urls.length) {
      await new Promise(resolve => setTimeout(resolve, options.delayBetweenRequests));
    }
  }
  
  // Sla de cache op aan het einde
  await scraper.saveCache();
  
  console.log(`\n✅ Verwerking voltooid: ${successful} succesvol, ${skipped} overgeslagen, ${failed} mislukt`);
}

/**
 * Leest bedrijfsnamen uit een bestand
 * @param {string} filePath - Pad naar het bestand
 * @returns {Promise<string[]>} - Array van bedrijfsnamen
 */
async function readNamesFromFile(filePath) {
  try {
    console.log(`Lezen van bedrijfsnamen uit bestand: ${filePath}`);
    
    // Lees bestandsinhoud
    const content = await fs.readFile(filePath, 'utf8');
    
    // Verwerk inhoud als tekstbestand met één naam per regel
    const names = content.split('\n')
      .map(line => line.trim())
      .filter(line => line);
    
    // Als er maar één regel is, probeer komma-gescheiden
    if (names.length === 1 && names[0].includes(',')) {
      return names[0].split(',')
        .map(name => name.trim())
        .filter(name => name);
    }
    
    console.log(`Gevonden bedrijfsnamen: ${names.length}`);
    return names;
  } catch (error) {
    console.error(`Fout bij lezen van bestand: ${error.message}`);
    throw error;
  }
}

/**
 * Verwerkt een lijst van bedrijfsnamen met de scraper
 * @param {string[]} names - Array van bedrijfsnamen
 * @param {string} country - Land voor de zoekopdracht
 * @param {Object} options - Scraper opties
 */
async function processNameList(names, country, options) {
  console.log(`\n🔍 Verwerken van ${names.length} bedrijfsnamen in ${country}...\n`);
  
  // Configureer scraper
  const scraper = new PlacesScraper({
    delayBetweenRequests: options.delayBetweenRequests,
    enableFirecrawlEnrichment: options.enableFirecrawlEnrichment,
    cacheProcessedUrls: true,
    logLevel: 'info'
  });
  
  // Initialiseer de scraper
  await scraper.initialize();
  
  let processed = 0;
  let successful = 0;
  let failed = 0;
  let skipped = 0;
  
  for (let i = 0; i < names.length; i++) {
    const name = names[i];
    
    try {
      console.log(`\n[${processed + 1}/${names.length}] Verwerken van bedrijfsnaam: ${name}`);
      
      // Verwerk de bedrijfsnaam met Places API en Firecrawl
      const result = await scraper.processCompanyName(name, country);
      
      if (result) {
        if (result.skipped) {
          skipped++;
          console.log(`⏭️ Overgeslagen: ${name} (al eerder verwerkt)`);
        } else {
          successful++;
          
          // Toon een samenvatting van de gevonden gegevens
          console.log(`✅ Succesvol verwerkt: ${name}`);
          
          if (result.placeDetails) {
            console.log(`  📍 Places API: ${result.placeDetails.name}`);
            if (result.placeDetails.formatted_address) {
              console.log(`     Adres: ${result.placeDetails.formatted_address}`);
            }
            if (result.placeDetails.website) {
              console.log(`     Website: ${result.placeDetails.website}`);
            }
          } else {
            console.log(`  ⚠️ Geen Places API gegevens gevonden`);
          }
          
          if (result.enrichedData) {
            console.log(`  🔍 Firecrawl: Data verrijkt`);
          } else {
            console.log(`  ⚠️ Geen Firecrawl verrijking`);
          }
        }
      } else {
        failed++;
        console.log(`⚠️ Geen data gevonden voor: ${name}`);
      }
    } catch (error) {
      failed++;
      console.error(`❌ Fout bij verwerken van ${name}: ${error.message}`);
    }
    
    processed++;
    
    // Toon voortgang
    console.log(`\nVoortgang: ${processed}/${names.length} (${successful} succesvol, ${skipped} overgeslagen, ${failed} mislukt)`);
    
    // Wacht even tussen verzoeken
    if (processed < names.length) {
      await new Promise(resolve => setTimeout(resolve, options.delayBetweenRequests));
    }
  }
  
  // Sla de cache op aan het einde
  await scraper.saveCache();
  
  console.log(`\n✅ Verwerking voltooid: ${successful} succesvol, ${skipped} overgeslagen, ${failed} mislukt`);
}

async function promptForJobTitle() {
  const { jobTitlesInput } = await inquirer.prompt([{
    type: 'input',
    name: 'jobTitlesInput',
    message: 'Voer functietitels in, gescheiden door komma\'s (bijv. "SROI, Social Return"):',
    validate: input => input.trim() !== '' ? true : 'Minstens één functietitel is verplicht'
  }]);
  
  // Split op komma's en trim elke term
  return jobTitlesInput.split(',')
    .map(term => term.trim())
    .filter(term => term !== '');
}

async function processJobSearch(jobTitles, location, options) {
  // Als het een string is, maak er een array van
  const titles = Array.isArray(jobTitles) ? jobTitles : [jobTitles];
  
  console.log(`\n🔍 Zoeken naar "${titles.join('", "')}" in ${location}...\n`);
  
  try {
    // Voer zoekopdracht uit via googleSearchService
    const searchParams = {
      jobTitles: titles,  // Nu als array
      location: location,
      maxResults: 10
    };
    
    const results = await googleSearchService.searchProfiles(searchParams);
    
    // Toon resultaten
    console.log(`\n✅ Zoekopdracht voltooid: ${results.length} profielen gevonden\n`);
    
    results.forEach((profile, index) => {
      console.log(`\nProfiel ${index + 1}:`);
      console.log(`Naam: ${profile.name}`);
      console.log(`Functie: ${profile.currentRole}`);
      console.log(`Bedrijf: ${profile.company}`);
      console.log(`Locatie: ${profile.location}`);
      console.log(`LinkedIn: ${profile.linkedInUrl}`);
      console.log(`Website: ${profile.website || 'Niet gevonden'}`);
      
      if (profile.enrichedData) {
        console.log('Verrijkte data:');
        console.log(JSON.stringify(profile.enrichedData, null, 2));
      }
    });
    
    // Toon OpenAI statistieken
    const stats = require('./services/openaiService').getStats();
    console.log('\nOpenAI API Statistieken:');
    console.log(`Totaal requests: ${stats.totalRequests}`);
    console.log(`Totaal tokens: ${stats.totalTokens}`);
    console.log(`Gemiddeld tokens per request: ${stats.averageTokensPerRequest}`);
    console.log(`Totaal errors: ${stats.totalErrors}`);
    
  } catch (error) {
    console.error(`\n❌ Fout bij zoekopdracht: ${error.message}`);
  }
}

/**
 * Hoofdfunctie voor de interactieve CLI
 */
async function main() {
  try {
    console.log('\n🔍 Google Places Scraper 🔍\n');
    
    // Zorg ervoor dat alle benodigde directories bestaan
    await ensureAllDirectories();
    
    // Valideer licentie (indien nodig)
    // await promptForLicense();
    
    // Vraag de gebruiker wat ze willen doen
    const { action } = await inquirer.prompt([{
      type: 'list',
      name: 'action',
      message: 'Wat wil je doen?',
      choices: [
        { name: 'Zoeken op locatie (grid-gebaseerd)', value: 'grid' },
        { name: 'Zoeken op locatie (alleen Places API - snel)', value: 'grid-fast' },
        { name: 'Zoeken op locatie (hybride - beperkte Firecrawl)', value: 'grid-hybrid' },
        { name: 'Zoeken op locatie (Apify - snel)', value: 'apify-fast' },
        { name: 'Zoeken op locatie (Apify hybride - beperkte Firecrawl)', value: 'apify-hybrid' },
        { name: 'Verwerken van URL-lijst uit bestand', value: 'urls' },
        { name: 'Zoeken op bedrijfsnamen', value: 'names' },
        { name: 'Zoeken op functie en plaats', value: 'job' }
      ]
    }]);
    
    if (action === 'grid') {
      // Bestaande grid-gebaseerde zoekfunctionaliteit
      // Vraag om locatie en converteer naar coördinaten
      const location = await promptForLocation();
      
      // Vraag om zoekradius
      const radius = await promptForRadius();
      
      // Vraag om zoektermen
      const keywords = await promptForKeywords();
      
      // Vraag om scraper opties
      const options = await promptForOptions();
      
      console.log('\n📋 Samenvatting van de zoekopdracht:');
      console.log(`📍 Locatie: ${location.name} (${location.lat}, ${location.lng})`);
      console.log(`⭕ Radius: ${radius} km`);
      console.log(`🔑 Zoektermen: ${keywords.join(', ')}`);
      console.log(`📊 Grid cel grootte: ${options.gridSizeM}m`);
      console.log(`⏱️ Vertraging tussen verzoeken: ${options.delayBetweenRequests}ms`);
      console.log(`🔄 Website verrijking: ${options.enableFirecrawlEnrichment ? 'Aan' : 'Uit'}`);
      
      const { confirm } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirm',
        message: 'Wil je doorgaan met deze instellingen?',
        default: true
      }]);
      
      if (!confirm) {
        console.log('\n❌ Zoekopdracht geannuleerd door gebruiker');
        return;
      }
      
      console.log('\n🚀 Starten van de scraper...\n');
      
      // Configureer en start de scraper
      const scraper = new PlacesScraper({
        delayBetweenRequests: options.delayBetweenRequests,
        enableFirecrawlEnrichment: options.enableFirecrawlEnrichment,
        logLevel: 'info'
      });
      
      await scraper.run({
        center: { lat: location.lat, lng: location.lng },
        radiusKm: radius,
        keywords: keywords,
        gridSizeM: options.gridSizeM
      });

    } else if (action === 'grid-fast') {
      // Nieuwe snelle grid-gebaseerde zoekfunctionaliteit (alleen Places API)
      // Vraag om locatie en converteer naar coördinaten
      const location = await promptForLocation();

      // Vraag om zoekradius
      const radius = await promptForRadius();

      // Vraag om zoektermen
      const keywords = await promptForKeywords();

      // Vraag om scraper opties (vereenvoudigd voor snelle modus)
      const { gridSize, delay } = await inquirer.prompt([
        {
          type: 'number',
          name: 'gridSize',
          message: 'Grootte van elke grid-cel in meters:',
          default: 500,
          validate: input => {
            const num = parseInt(input);
            if (isNaN(num) || num <= 0) return 'Voer een positief getal in';
            return true;
          }
        },
        {
          type: 'number',
          name: 'delay',
          message: 'Vertraging tussen API-verzoeken in milliseconden:',
          default: 200,
          validate: input => {
            const num = parseInt(input);
            if (isNaN(num) || num < 100) return 'Minimaal 100ms vertraging is vereist';
            return true;
          }
        }
      ]);

      const options = {
        gridSizeM: gridSize,
        delayBetweenRequests: delay,
        enableFirecrawlEnrichment: false // Firecrawl uitgeschakeld voor snelle modus
      };

      console.log('\n📋 Samenvatting van de snelle zoekopdracht:');
      console.log(`📍 Locatie: ${location.name} (${location.lat}, ${location.lng})`);
      console.log(`⭕ Radius: ${radius} km`);
      console.log(`🔑 Zoektermen: ${keywords.join(', ')}`);
      console.log(`📊 Grid cel grootte: ${options.gridSizeM}m`);
      console.log(`⏱️ Vertraging tussen verzoeken: ${options.delayBetweenRequests}ms`);
      console.log(`🚀 Snelle modus: Alleen Places API (geen website verrijking)`);

      const { confirm } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirm',
        message: 'Wil je doorgaan met deze snelle zoekopdracht?',
        default: true
      }]);

      if (!confirm) {
        console.log('\n❌ Zoekopdracht geannuleerd door gebruiker');
        return;
      }

      console.log('\n🚀 Starten van de snelle scraper...\n');

      // Configureer en start de scraper
      const scraper = new PlacesScraper({
        delayBetweenRequests: options.delayBetweenRequests,
        enableFirecrawlEnrichment: false, // Firecrawl uitgeschakeld
        logLevel: 'info'
      });

      await scraper.run({
        center: { lat: location.lat, lng: location.lng },
        radiusKm: radius,
        keywords: keywords,
        gridSizeM: options.gridSizeM
      });

    } else if (action === 'grid-hybrid') {
      // Nieuwe hybride grid-gebaseerde zoekfunctionaliteit (Places API + beperkte Firecrawl)
      // Vraag om locatie en converteer naar coördinaten
      const location = await promptForLocation();

      // Vraag om zoekradius
      const radius = await promptForRadius();

      // Vraag om zoektermen
      const keywords = await promptForKeywords();

      // Vraag om scraper opties (vereenvoudigd voor hybride modus)
      const { gridSize, delay } = await inquirer.prompt([
        {
          type: 'number',
          name: 'gridSize',
          message: 'Grootte van elke grid-cel in meters:',
          default: 500,
          validate: input => {
            const num = parseInt(input);
            if (isNaN(num) || num <= 0) return 'Voer een positief getal in';
            return true;
          }
        },
        {
          type: 'number',
          name: 'delay',
          message: 'Vertraging tussen API-verzoeken in milliseconden:',
          default: 500,
          validate: input => {
            const num = parseInt(input);
            if (isNaN(num) || num < 200) return 'Minimaal 200ms vertraging is vereist';
            return true;
          }
        }
      ]);

      const options = {
        gridSizeM: gridSize,
        delayBetweenRequests: delay,
        enableFirecrawlEnrichment: true,
        firecrawlMode: 'limited' // Hybride modus: beperkte Firecrawl data
      };

      console.log('\n📋 Samenvatting van de hybride zoekopdracht:');
      console.log(`📍 Locatie: ${location.name} (${location.lat}, ${location.lng})`);
      console.log(`⭕ Radius: ${radius} km`);
      console.log(`🔑 Zoektermen: ${keywords.join(', ')}`);
      console.log(`📊 Grid cel grootte: ${options.gridSizeM}m`);
      console.log(`⏱️ Vertraging tussen verzoeken: ${options.delayBetweenRequests}ms`);
      console.log(`🔄 Hybride modus: Places API + beperkte Firecrawl (decision_maker_name, role_title, email, summary)`);

      const { confirm } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirm',
        message: 'Wil je doorgaan met deze hybride instellingen?',
        default: true
      }]);

      if (!confirm) {
        console.log('\n❌ Hybride zoekopdracht geannuleerd door gebruiker');
        return;
      }

      console.log('\n🚀 Starten van de hybride scraper...\n');

      // Configureer en start de scraper
      const scraper = new PlacesScraper({
        delayBetweenRequests: options.delayBetweenRequests,
        enableFirecrawlEnrichment: true,
        firecrawlMode: 'limited', // Hybride modus
        logLevel: 'info'
      });

      await scraper.run({
        center: { lat: location.lat, lng: location.lng },
        radiusKm: radius,
        keywords: keywords,
        gridSizeM: options.gridSizeM
      });

    } else if (action === 'apify-fast') {
      // Nieuwe Apify snelle zoekfunctionaliteit (alleen Apify API)
      // Vraag om locatie
      const { location } = await inquirer.prompt([{
        type: 'input',
        name: 'location',
        message: 'Voer de locatie in (bijv. "Amsterdam, Netherlands"):',
        validate: input => input.trim().length > 0 || 'Locatie is verplicht'
      }]);

      // Vraag om zoektermen
      const keywords = await promptForKeywords();

      // Vraag om scraper opties
      const options = await promptForScraperOptions();

      console.log('\n📋 Apify Fast configuratie:');
      console.log(`📍 Locatie: ${location}`);
      console.log(`🔑 Zoektermen: ${keywords.join(', ')}`);
      console.log(`🚀 Modus: Snelle Apify scraping (alleen Apify API)`);
      console.log(`⏱️ Vertraging: ${options.delayBetweenRequests}ms`);
      console.log(`📊 Max plaatsen per zoekterm: ${options.maxPlacesPerSearch || 50}`);

      const { confirm } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirm',
        message: 'Wil je doorgaan met deze configuratie?',
        default: true
      }]);

      if (!confirm) {
        console.log('\n❌ Scraping geannuleerd door gebruiker');
        return;
      }

      console.log('\n🚀 Starten van de snelle Apify scraper...\n');

      // Configureer en start de Apify scraper
      const scraper = new ApifyScraper({
        delayBetweenRequests: options.delayBetweenRequests,
        maxPlacesPerSearch: options.maxPlacesPerSearch || 50,
        enableFirecrawlEnrichment: false, // Firecrawl uitgeschakeld
        logLevel: 'info'
      });

      await scraper.run({
        location: location,
        keywords: keywords
      });

    } else if (action === 'apify-hybrid') {
      // Nieuwe Apify hybride zoekfunctionaliteit (Apify API + beperkte Firecrawl)
      // Vraag om locatie
      const { location } = await inquirer.prompt([{
        type: 'input',
        name: 'location',
        message: 'Voer de locatie in (bijv. "Amsterdam, Netherlands"):',
        validate: input => input.trim().length > 0 || 'Locatie is verplicht'
      }]);

      // Vraag om zoektermen
      const keywords = await promptForKeywords();

      // Vraag om scraper opties
      const options = await promptForScraperOptions();

      console.log('\n📋 Apify Hybrid configuratie:');
      console.log(`📍 Locatie: ${location}`);
      console.log(`🔑 Zoektermen: ${keywords.join(', ')}`);
      console.log(`🔄 Modus: Hybride Apify scraping (Apify API + beperkte Firecrawl)`);
      console.log(`⏱️ Vertraging: ${options.delayBetweenRequests}ms`);
      console.log(`📊 Max plaatsen per zoekterm: ${options.maxPlacesPerSearch || 50}`);

      const { confirm } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirm',
        message: 'Wil je doorgaan met deze configuratie?',
        default: true
      }]);

      if (!confirm) {
        console.log('\n❌ Scraping geannuleerd door gebruiker');
        return;
      }

      console.log('\n🚀 Starten van de hybride Apify scraper...\n');

      // Configureer en start de Apify scraper
      const scraper = new ApifyScraper({
        delayBetweenRequests: options.delayBetweenRequests,
        maxPlacesPerSearch: options.maxPlacesPerSearch || 50,
        enableFirecrawlEnrichment: true,
        firecrawlMode: 'limited', // Hybride modus
        logLevel: 'info'
      });

      await scraper.run({
        location: location,
        keywords: keywords
      });

    } else if (action === 'urls') {
      // Nieuwe URL-lijst functionaliteit
      // Vraag om bestand met URL's
      const filePath = await promptForUrlFile();
      
      // Lees URL's uit bestand
      const urls = await readUrlsFromFile(filePath);
      
      if (urls.length === 0) {
        console.log('\n❌ Geen geldige URL\'s gevonden in het bestand');
        return;
      }
      
      console.log(`\n📋 Gevonden URL's (${urls.length}):`);
      urls.slice(0, 10).forEach((url, index) => {
        console.log(`${index + 1}. ${url}`);
      });
      
      if (urls.length > 10) {
        console.log(`... en ${urls.length - 10} meer`);
      }
      
      // Vraag om scraper opties (vereenvoudigd voor URL-lijst)
      const { delay, enableEnrichment } = await inquirer.prompt([
        {
          type: 'number',
          name: 'delay',
          message: 'Vertraging tussen verzoeken in milliseconden:',
          default: 1000,
          validate: input => {
            const num = parseInt(input);
            if (isNaN(num) || num < 500) return 'Minimaal 500ms vertraging is vereist';
            return true;
          }
        },
        {
          type: 'confirm',
          name: 'enableEnrichment',
          message: 'Website data verrijken met Firecrawl?',
          default: true
        }
      ]);
      
      const options = {
        delayBetweenRequests: delay,
        enableFirecrawlEnrichment: enableEnrichment
      };
      
      const { confirm } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirm',
        message: 'Wil je doorgaan met het verwerken van deze URL\'s?',
        default: true
      }]);
      
      if (!confirm) {
        console.log('\n❌ Verwerking geannuleerd door gebruiker');
        return;
      }
      
      console.log('\n🚀 Starten met verwerken van URL\'s...\n');
      
      // Verwerk de URL's
      await processUrlList(urls, options);
    } else if (action === 'names') {
      // Nieuwe functionaliteit voor zoeken op bedrijfsnamen
      const { nameFile, country } = await inquirer.prompt([
        {
          type: 'input',
          name: 'nameFile',
          message: 'Voer het pad naar het bestand met bedrijfsnamen in:',
          validate: async (input) => {
            try {
              const stats = await fs.stat(input);
              if (!stats.isFile()) return 'Dit is geen bestand';
              return true;
            } catch (error) {
              return 'Bestand bestaat niet of is niet toegankelijk';
            }
          }
        },
        {
          type: 'input',
          name: 'country',
          message: 'Voer het land voor de zoekopdracht in (bijv. "Nederland"):',
          validate: input => input.trim() !== '' ? true : 'Land is verplicht'
        }
      ]);
      
      if (nameFile && country) {
        console.log(`\n🔍 Verwerken van ${nameFile} voor ${country}...\n`);
        
        // Lees bedrijfsnamen uit bestand
        const names = await readNamesFromFile(nameFile);
        
        if (names.length === 0) {
          console.log('\n❌ Geen geldige bedrijfsnamen gevonden in het bestand');
          return;
        }
        
        console.log(`\n📋 Gevonden bedrijfsnamen (${names.length}):`);
        names.slice(0, 10).forEach((name, index) => {
          console.log(`${index + 1}. ${name}`);
        });
        
        if (names.length > 10) {
          console.log(`... en ${names.length - 10} meer`);
        }
        
        // Verwerk de bedrijfsnamen
        await processNameList(names, country, {
          delayBetweenRequests: 1000,
          enableFirecrawlEnrichment: true
        });
      }
    } else if (action === 'job') {
      // Vraag om functietitels (nu als array)
      const jobTitles = await promptForJobTitle();
      
      // Vraag om locatie (nu optioneel)
      const { location } = await inquirer.prompt([{
        type: 'input',
        name: 'location',
        message: 'Voer een plaatsnaam in (optioneel):',
        default: ''
      }]);
      
      console.log('\n📋 Samenvatting van de zoekopdracht:');
      console.log(`🔍 Functies: ${jobTitles.join(', ')}`);
      if (location) {
        console.log(`📍 Locatie: ${location}`);
      }
      
      const { confirm } = await inquirer.prompt([{
        type: 'confirm',
        name: 'confirm',
        message: 'Wil je doorgaan met deze zoekopdracht?',
        default: true
      }]);
      
      if (!confirm) {
        console.log('\n❌ Zoekopdracht geannuleerd door gebruiker');
        return;
      }
      
      console.log('\n🚀 Starten van de zoekopdracht...\n');
      
      // Verwerk de zoekopdracht met array van functietitels
      await processJobSearch(jobTitles, location, {});
    }
    
    console.log('\n✅ Taak voltooid!');
    
  } catch (error) {
    console.error('\n❌ Fout:', error.message);
    console.error('Details:', error.stack);
  }
}

// Verbeterde error handling voor onverwachte fouten
process.on('uncaughtException', async (error) => {
  console.error('❌ Onverwachte fout:', error.message);
  console.error('Details:', error.stack);
  await new Promise(resolve => setTimeout(resolve, 5000));
  process.exit(1);
});

process.on('unhandledRejection', async (error) => {
  console.error('❌ Onafgehandelde promise rejection:', error.message);
  console.error('Details:', error.stack);
  await new Promise(resolve => setTimeout(resolve, 5000));
  process.exit(1);
});

// Start de applicatie
if (require.main === module) {
  // Check of er command-line argumenten zijn meegegeven
  const hasCommandLineArgs = process.argv.length > 2;
  
  if (hasCommandLineArgs) {
    // Command-line modus
    program
      .version(pkg.version)
      .description('Google Places Scraper met grid-gebaseerde zoekstrategie')
      .option('-c, --center <lat,lng>', 'Middelpunt coördinaten (bijv. "52.3676,4.9041")')
      .option('-r, --radius <km>', 'Straal in kilometers rond het middelpunt', parseFloat)
      .option('-k, --keywords <keywords>', 'Zoektermen gescheiden door komma\'s')
      .option('-g, --grid-size <meters>', 'Grootte van elke grid-cel in meters (default: 500)', parseInt, 500)
      .option('-d, --delay <ms>', 'Vertraging tussen API-verzoeken in milliseconden', parseInt, 200)
      .option('-l, --log-level <level>', 'Log niveau (debug, info, warn, error)', 'info')
      .option('--no-cache', 'Schakel caching van verwerkte plaatsen uit')
      .option('--no-enrich', 'Schakel Firecrawl verrijking uit')
      .option('--hybrid-enrich', 'Gebruik hybride Firecrawl modus (beperkte velden)')
      .option('-u, --url-file <path>', 'Pad naar bestand met URL\'s om te verwerken')
      .option('-n, --name-file <path>', 'Pad naar bestand met bedrijfsnamen om te verwerken')
      .option('--country <country>', 'Land voor bedrijfsnaam zoekopdrachten (bijv. "Nederland")')
      .option('-j, --job-title <titles>', 'Functietitels voor zoekopdracht, gescheiden door komma\'s (bijv. "SROI, Social Return")')
      .option('--location <location>', 'Plaatsnaam voor zoekopdracht (bijv. "Amsterdam")')
      .parse(process.argv);

    const options = program.opts();

    // URL-bestand modus
    if (options.urlFile) {
      (async () => {
        try {
          const urls = await readUrlsFromFile(options.urlFile);
          
          if (urls.length === 0) {
            console.error('Geen geldige URL\'s gevonden in het bestand');
            process.exit(1);
          }
          
          console.log(`Verwerken van ${urls.length} URL's uit bestand: ${options.urlFile}`);
          
          // Bepaal Firecrawl modus voor URL-lijst
          let firecrawlMode = 'full';
          let enableFirecrawl = options.enrich !== false;

          if (options.hybridEnrich) {
            firecrawlMode = 'limited';
            enableFirecrawl = true;
          } else if (options.enrich === false) {
            firecrawlMode = 'disabled';
            enableFirecrawl = false;
          }

          // Configureer scraper
          const scraper = new PlacesScraper({
            delayBetweenRequests: options.delay || 1000,
            logLevel: options.logLevel,
            enableFirecrawlEnrichment: enableFirecrawl,
            firecrawlMode: firecrawlMode
          });

          await processUrlList(urls, {
            delayBetweenRequests: options.delay || 1000,
            enableFirecrawlEnrichment: enableFirecrawl,
            firecrawlMode: firecrawlMode
          });
          
        } catch (error) {
          console.error(`Fatale fout: ${error.message}`);
          process.exit(1);
        }
      })();
      return;
    }

    // Grid-gebaseerde modus (bestaande code)
    // Valideer verplichte opties
    if (!options.center || !options.radius || !options.keywords) {
      console.error('Fout: --center, --radius en --keywords zijn verplicht voor grid-gebaseerde zoekopdrachten');
      program.help();
      process.exit(1);
    }

    // Parse center coördinaten
    const [lat, lng] = options.center.split(',').map(parseFloat);
    if (isNaN(lat) || isNaN(lng)) {
      console.error('Fout: Ongeldige center coördinaten. Gebruik format "lat,lng" (bijv. "52.3676,4.9041")');
      process.exit(1);
    }

    // Parse keywords
    const keywords = options.keywords.split(',').map(k => k.trim());

    // Bepaal Firecrawl modus
    let firecrawlMode = 'full';
    let enableFirecrawl = options.enrich !== false;

    if (options.hybridEnrich) {
      firecrawlMode = 'limited';
      enableFirecrawl = true;
    } else if (options.enrich === false) {
      firecrawlMode = 'disabled';
      enableFirecrawl = false;
    }

    // Configureer scraper
    const scraper = new PlacesScraper({
      delayBetweenRequests: options.delay,
      cacheProcessedPlaces: options.cache !== false,
      logLevel: options.logLevel,
      enableFirecrawlEnrichment: enableFirecrawl,
      firecrawlMode: firecrawlMode
    });

    // Start scraper
    (async () => {
      try {
        await scraper.run({
          center: { lat, lng },
          radiusKm: options.radius,
          keywords: keywords,
          gridSizeM: options.gridSize || 500
        });
      } catch (error) {
        console.error(`Fatale fout: ${error.message}`);
        process.exit(1);
      }
    })();

    // In de command-line modus
    if (options.nameFile) {
      (async () => {
        try {
          // Controleer of het land is opgegeven
          if (!options.country) {
            console.error('Fout: --country is verplicht bij gebruik van --name-file');
            process.exit(1);
          }
          
          const names = await readNamesFromFile(options.nameFile);
          
          if (names.length === 0) {
            console.error('Geen geldige bedrijfsnamen gevonden in het bestand');
            process.exit(1);
          }
          
          console.log(`Verwerken van ${names.length} bedrijfsnamen uit bestand: ${options.nameFile}`);
          
          await processNameList(names, options.country, {
            delayBetweenRequests: options.delay || 1000,
            enableFirecrawlEnrichment: options.enrich !== false
          });
          
        } catch (error) {
          console.error(`Fatale fout: ${error.message}`);
          process.exit(1);
        }
      })();
      return;
    }

    // Voeg een nieuwe tak toe voor job-title:
    if (options.jobTitle) {
      (async () => {
        try {
          // Controleer of er een locatie is opgegeven
          if (!options.location) {
            console.error('Fout: --location is verplicht bij gebruik van --job-title');
            process.exit(1);
          }
          
          // Split de job titles op komma's
          const jobTitles = options.jobTitle.split(',')
            .map(term => term.trim())
            .filter(term => term !== '');
          
          console.log(`Zoeken naar "${jobTitles.join('", "')}" in ${options.location}...`);
          
          // Verwerk de zoekopdracht met array van functietitels
          await processJobSearch(jobTitles, options.location, {});
          
        } catch (error) {
          console.error(`Fatale fout: ${error.message}`);
          process.exit(1);
        }
      })();
      return;
    }
  } else {
    // Interactieve modus
    main().catch(async (error) => {
      console.error('❌ Start-up fout:', error.message);
      console.error('Details:', error.stack);
      await new Promise(resolve => setTimeout(resolve, 5000));
      process.exit(1);
    });
  }
}