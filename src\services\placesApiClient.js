/**
 * Google Places API Client
 * Biedt methodes voor het interageren met de Google Places API
 */

require('dotenv').config();
const axios = require('axios');

const GOOGLE_PLACES_API_KEY = process.env.GOOGLE_PLACES_API_KEY;
const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY || GOOGLE_PLACES_API_KEY;

// Base URL voor Google Places API requests
const PLACES_API_BASE_URL = 'https://maps.googleapis.com/maps/api/place';
const API_KEY = process.env.GOOGLE_PLACES_API_KEY;

// Controleren of API key is ingesteld
if (!API_KEY) {
  throw new Error('GOOGLE_PLACES_API_KEY niet gevonden in .env bestand');
}

// Endpoints
const NEARBY_SEARCH_ENDPOINT = `${PLACES_API_BASE_URL}/nearbysearch/json`;
const TEXT_SEARCH_ENDPOINT = `${PLACES_API_BASE_URL}/textsearch/json`;
const DETAILS_ENDPOINT = `${PLACES_API_BASE_URL}/details/json`;

/**
 * Basisklasse voor het uitvoeren van verzoeken naar de Google Places API
 */
class PlacesApiClient {
  /**
   * Voert een tekstzoekopdracht uit
   * @param {string} query - Zoektekst (bijv. "restaurants in Amsterdam")
   * @param {Object} options - Extra opties zoals taal en regio
   * @returns {Promise<Array>} De zoekresultaten
   */
  async textSearch(query, options = {}) {
    const params = {
      query,
      key: API_KEY,
      ...options
    };
    
    return this._makeRequest('/textsearch/json', params);
  }

  /**
   * Zoekt naar plaatsen in de buurt van een bepaalde locatie
   * @param {Object} location - Latitude en longitude {lat: number, lng: number}
   * @param {number} radius - Zoekradius in meters (max 50000)
   * @param {string} type - Type plaats (restaurant, cafe, etc.)
   * @param {Object} options - Extra opties
   * @returns {Promise<Array>} De zoekresultaten
   */
  async nearbySearch(location, radius, type, options = {}) {
    const params = {
      location: `${location.lat},${location.lng}`,
      radius,
      type,
      key: API_KEY,
      ...options
    };
    
    return this._makeRequest('/nearbysearch/json', params);
  }

  /**
   * Haalt gedetailleerde informatie op over een specifieke plaats
   * @param {string} placeId - De Google Places ID
   * @param {Object} options - Extra opties zoals velden en taal
   * @returns {Promise<Object>} Gedetailleerde informatie over de plaats
   */
  async getPlaceDetails(placeId, options = {}) {
    const params = {
      place_id: placeId,
      key: API_KEY,
      ...options
    };
    
    const response = await this._makeRequest('/details/json', params);
    return response.result;
  }

  /**
   * Haalt foto's op van een plaats
   * @param {string} photoReference - Foto referentie van Google Places API
   * @param {number} maxWidth - Maximale breedte van de foto
   * @param {number} maxHeight - Maximale hoogte van de foto (optioneel)
   * @returns {string} URL van de foto
   */
  getPhotoUrl(photoReference, maxWidth = 400, maxHeight = null) {
    let params = `key=${API_KEY}&photoreference=${photoReference}&maxwidth=${maxWidth}`;
    
    if (maxHeight) {
      params += `&maxheight=${maxHeight}`;
    }
    
    return `${PLACES_API_BASE_URL}/photo?${params}`;
  }

  /**
   * Voert automatische aanvulling uit voor zoektermen
   * @param {string} input - Gedeeltelijke zoektekst
   * @param {Object} options - Extra opties zoals locatie en radius
   * @returns {Promise<Array>} Suggesties voor automatisch aanvullen
   */
  async autocomplete(input, options = {}) {
    const params = {
      input,
      key: API_KEY,
      ...options
    };
    
    const response = await this._makeRequest('/autocomplete/json', params);
    return response.predictions;
  }

  /**
   * Zoekt naar plaatsen in de buurt van een locatie
   * @param {Object} params - Parameters voor de zoekopdracht
   * @param {Object} params.location - Locatie {lat, lng}
   * @param {number} params.radius - Zoekradius in meters
   * @param {string} [params.keyword] - Zoekterm
   * @param {string} [params.type] - Type plaats
   * @param {string} [params.language] - Taalcode (bijv. 'nl')
   * @param {string} [params.pagetoken] - Token voor paginering
   * @returns {Promise<Object>} - Zoekresultaten
   */
  async nearbySearch(params) {
    try {
      const response = await axios.get(NEARBY_SEARCH_ENDPOINT, {
        params: {
          key: GOOGLE_PLACES_API_KEY,
          location: params.location ? `${params.location.lat},${params.location.lng}` : undefined,
          radius: params.radius,
          keyword: params.keyword,
          type: params.type,
          language: params.language,
          pagetoken: params.pagetoken
        }
      });
      
      return response.data;
    } catch (error) {
      console.error(`Places API fout (nearby search): ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Zoekt naar plaatsen op basis van een zoekopdracht
   * @param {Object} params - Parameters voor de zoekopdracht
   * @param {string} params.query - Zoekopdracht
   * @param {string} [params.type] - Type plaats
   * @param {string} [params.language] - Taalcode (bijv. 'nl')
   * @param {string} [params.pagetoken] - Token voor paginering
   * @returns {Promise<Object>} - Zoekresultaten
   */
  async searchPlaces(params) {
    try {
      const response = await axios.get(TEXT_SEARCH_ENDPOINT, {
        params: {
          key: GOOGLE_MAPS_API_KEY,
          query: params.query,
          type: params.type,
          language: params.language,
          pagetoken: params.pagetoken
        }
      });
      
      return response.data;
    } catch (error) {
      console.error(`Places API fout (text search): ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Haalt details op van een plaats
   * @param {string} placeId - ID van de plaats
   * @param {Object} [options] - Opties
   * @param {string} [options.fields] - Komma-gescheiden lijst van velden
   * @param {string} [options.language] - Taalcode (bijv. 'nl')
   * @returns {Promise<Object>} - Details van de plaats
   */
  async getPlaceDetails(placeId, options = {}) {
    try {
      const response = await axios.get(DETAILS_ENDPOINT, {
        params: {
          key: GOOGLE_MAPS_API_KEY,
          place_id: placeId,
          fields: options.fields,
          language: options.language
        }
      });
      
      return response.data.result;
    } catch (error) {
      console.error(`Places API fout (details): ${error.message}`);
      throw error;
    }
  }

  /**
   * Hulpmethode voor het uitvoeren van API-verzoeken
   * @private
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} API response data
   */
  async _makeRequest(endpoint, params) {
    try {
      const url = `${PLACES_API_BASE_URL}${endpoint}`;
      const response = await axios.get(url, { params });
      
      if (response.data.status !== 'OK' && response.data.status !== 'ZERO_RESULTS') {
        throw new Error(`Places API fout: ${response.data.status} - ${response.data.error_message || 'Geen foutdetails beschikbaar'}`);
      }
      
      return response.data;
    } catch (error) {
      if (error.response) {
        console.error('API Foutdetails:', error.response.data);
        throw new Error(`Places API request mislukt: ${error.response.status} - ${error.message}`);
      }
      throw error;
    }
  }
}

module.exports = new PlacesApiClient();