require('dotenv').config();
const { mapPlacesWithLimitedFirecrawlToAirtableFields } = require('./services/airtableService');
const Airtable = require('airtable');

// Airtable configuratie
const accessToken = process.env.AIRTABLE_ACCESS_TOKEN;
const baseId = process.env.AIRTABLE_BASE_ID;
const tableName = process.env.AIRTABLE_TABLE_NAME || 'Places';

if (!accessToken || !baseId) {
  console.error('Fout: AIRTABLE_ACCESS_TOKEN en AIRTABLE_BASE_ID moeten worden ingesteld in het .env bestand');
  process.exit(1);
}

const base = new Airtable({ apiKey: accessToken }).base(baseId);

async function testHybridMapping() {
  console.log('🧪 Test hybride mapping functie...\n');

  // Mock Places API data
  const mockPlaceDetails = {
    name: 'Test Restaurant BV',
    formatted_address: 'Teststraat 123, 1234 AB Amsterdam, Nederland',
    formatted_phone_number: '020-1234567',
    website: 'https://testrestaurant.nl',
    url: 'https://maps.google.com/?cid=*********',
    rating: 4.5,
    user_ratings_total: 150,
    types: ['restaurant', 'food', 'establishment']
  };

  // Mock beperkte Firecrawl data (alleen de 4 extra velden)
  const mockEnrichedData = {
    contact_person: {
      name: 'Jan de Kok',
      role: 'Eigenaar/Chef-kok',
      email: '<EMAIL>',
      phone: '06-12345678'
    },
    company_contact: {
      email: '<EMAIL>',
      phone: '020-1234567'
    },
    companySummary: 'Test Restaurant BV is een gezellig familierestaurant in het hart van Amsterdam. Wij serveren verse, seizoensgebonden gerechten met een moderne twist op klassieke Nederlandse keuken. Ons team van ervaren koks gebruikt alleen de beste ingrediënten van lokale leveranciers.'
  };

  // Test de hybride mapping functie
  console.log('1. Test hybride mapping functie...');
  const mappedFields = mapPlacesWithLimitedFirecrawlToAirtableFields(mockPlaceDetails, mockEnrichedData);
  
  console.log('✅ Gemapte velden (hybride modus):');
  Object.entries(mappedFields).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });

  // Controleer specifieke hybride velden
  console.log('\n🔍 Controle hybride specifieke velden:');
  console.log(`✅ decision_maker_name: ${mappedFields.decision_maker_name ? 'AANWEZIG' : 'ONTBREEKT'} (${mappedFields.decision_maker_name || 'N/A'})`);
  console.log(`✅ role_title: ${mappedFields.role_title ? 'AANWEZIG' : 'ONTBREEKT'} (${mappedFields.role_title || 'N/A'})`);
  console.log(`✅ email: ${mappedFields.email ? 'AANWEZIG' : 'ONTBREEKT'} (${mappedFields.email || 'N/A'})`);
  console.log(`✅ summary: ${mappedFields.summary ? 'AANWEZIG' : 'ONTBREEKT'} (${mappedFields.summary ? mappedFields.summary.substring(0, 50) + '...' : 'N/A'})`);

  // Controleer basis Places API velden
  console.log('\n🔍 Controle basis Places API velden:');
  console.log(`✅ company_name: ${mappedFields.company_name ? 'AANWEZIG' : 'ONTBREEKT'} (${mappedFields.company_name || 'N/A'})`);
  console.log(`✅ phone: ${mappedFields.phone ? 'AANWEZIG' : 'ONTBREEKT'} (${mappedFields.phone || 'N/A'})`);
  console.log(`✅ website: ${mappedFields.website ? 'AANWEZIG' : 'ONTBREEKT'} (${mappedFields.website || 'N/A'})`);
  console.log(`✅ address_street: ${mappedFields.address_street ? 'AANWEZIG' : 'ONTBREEKT'} (${mappedFields.address_street || 'N/A'})`);

  // Test zonder Firecrawl data (fallback naar basis mapping)
  console.log('\n2. Test hybride mapping zonder Firecrawl data (fallback)...');
  const mappedFieldsNoEnrichment = mapPlacesWithLimitedFirecrawlToAirtableFields(mockPlaceDetails, null);
  
  console.log('✅ Gemapte velden (fallback naar basis):');
  console.log(`   company_name: ${mappedFieldsNoEnrichment.company_name}`);
  console.log(`   phone: ${mappedFieldsNoEnrichment.phone}`);
  console.log(`   website: ${mappedFieldsNoEnrichment.website}`);
  console.log(`   decision_maker_name: ${mappedFieldsNoEnrichment.decision_maker_name || 'NIET AANWEZIG'}`);
  console.log(`   email: ${mappedFieldsNoEnrichment.email || 'NIET AANWEZIG'}`);

  // Optioneel: Test opslaan in Airtable
  const { testSave } = await require('inquirer').prompt([{
    type: 'confirm',
    name: 'testSave',
    message: 'Wil je de hybride mapping testen door op te slaan in Airtable?',
    default: false
  }]);

  if (testSave) {
    console.log('\n3. Test opslaan in Airtable...');
    try {
      await base(tableName).create([{ fields: mappedFields }], { typecast: true });
      console.log('✅ Hybride record succesvol opgeslagen in Airtable!');
    } catch (error) {
      console.error('❌ Fout bij opslaan in Airtable:', error.message);
    }
  }

  console.log('\n🎉 Hybride mapping test voltooid!');
  console.log('\n💡 Hybride modus kenmerken:');
  console.log('- Basis Places API velden: company_name, phone, website, adres');
  console.log('- Extra Firecrawl velden: decision_maker_name, role_title, email, summary');
  console.log('- Sneller dan volledige Firecrawl verrijking');
  console.log('- Meer contactgegevens dan alleen Places API');
}

// Voer test uit als dit script direct wordt aangeroepen
if (require.main === module) {
  testHybridMapping();
}

module.exports = testHybridMapping;
