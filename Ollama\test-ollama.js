const readline = require('readline');
const fetch = require('node-fetch'); // Zorg dat je node-fetch installeert: npm install node-fetch

// Configuratie
const SERVER_IP = '***********'; // Vervang dit met je server IP
const PORT = '11434';
const MODEL = 'gemma3:1b'; // Of een ander model dat je hebt geïnstalleerd op de server

// Maak readline interface voor gebruikersinput
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Functie om vraag aan Ollama te stellen
async function askOllama(question) {
  try {
    const response = await fetch(`http://${SERVER_IP}:${PORT}/api/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: MODEL,
        prompt: question,
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.response;
  } catch (error) {
    console.error('Error bij verbinden met Ollama:', error.message);
    return null;
  }
}

// Hoofdfunctie die de interactieve loop uitvoert
async function main() {
  console.log(`\n🤖 Ollama Test Client`);
  console.log(`Verbinding met: http://${SERVER_IP}:${PORT}`);
  console.log(`Model: ${MODEL}`);
  console.log(`\nType 'exit' om te stoppen\n`);

  // Interactieve loop
  while (true) {
    const question = await new Promise(resolve => {
      rl.question('Jouw vraag: ', resolve);
    });

    // Check voor exit commando
    if (question.toLowerCase() === 'exit') {
      console.log('\nTot ziens! 👋');
      rl.close();
      break;
    }

    // Vraag stellen aan Ollama
    console.log('\nEven denken... 🤔\n');
    const answer = await askOllama(question);
    
    if (answer) {
      console.log('Antwoord:', answer);
    }
    console.log('\n-------------------\n');
  }
}

// Start het script
main().catch(error => {
  console.error('Onverwachte fout:', error);
  rl.close();
}); 