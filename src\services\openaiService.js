const { OpenAI } = require('openai');
const logger = require('./logger');

class OpenAIService {
  constructor(options = {}) {
    this.options = {
      model: 'gpt-3.5-turbo',
      maxTokens: 100, // Verlaagd voor kortere responses
      temperature: 0.3,
      maxRetries: 3,
      delayBetweenRequests: 1000,
      ...options
    };
    
    this.client = null;
    this.totalRequests = 0;
    this.totalTokens = 0;
    this.totalErrors = 0;
  }

  /**
   * Initialiseert de OpenAI client
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.client) return;

    try {
      const apiKey = process.env.OPENAI_API_KEY;
      if (!apiKey) {
        throw new Error('OpenAI API key ontbreekt');
      }

      this.client = new OpenAI({ apiKey });
      logger.info('OpenAI service geïnitialiseerd');
    } catch (error) {
      logger.error('Fout bij initialiseren OpenAI service:', error);
      throw error;
    }
  }

  /**
   * Extraheert basis informatie uit een LinkedIn profiel
   * @param {Object} result - Zoekresultaat
   * @returns {Promise<Object|null>} - Basis informatie of null
   */
  async extractBasicInfo(result) {
    if (!this.client) await this.initialize();

    try {
      const prompt = this.buildExtractionPrompt(result);
      
      const response = await this.client.chat.completions.create({
        model: this.options.model,
        messages: [
          { 
            role: 'system', 
            content: 'Je bent een assistent die LinkedIn profielen analyseert. Extraheer alleen de meest essentiële informatie in JSON formaat.'
          },
          { role: 'user', content: prompt }
        ],
        max_tokens: this.options.maxTokens,
        temperature: this.options.temperature
      });

      this.totalRequests++;
      this.totalTokens += response.usage.total_tokens;

      const content = response.choices[0].message.content;
      return this.parseExtractionResponse(content);

    } catch (error) {
      logger.error('Fout bij extraheren basis informatie:', error);
      this.totalErrors++;
      return null;
    }
  }

  /**
   * Bouwt de prompt voor profiel informatie extractie
   * @param {Object} result - Zoekresultaat
   * @returns {string} - Prompt
   */
  buildExtractionPrompt(result) {
    const { title, snippet } = result;

    return `Analyseer dit LinkedIn profiel en extraheer alleen deze velden in JSON formaat:

Titel: ${title}
Beschrijving: ${snippet}

Extraheer alleen:
- name: Volledige naam
- currentRole: Huidige functietitel
- company: Huidige bedrijfsnaam
- location: Locatie (stad/regio)

Retourneer alleen de JSON, zonder extra tekst. Gebruik null voor ontbrekende waarden.`;
  }

  /**
   * Parseert de extractie response
   * @param {string} content - Response content
   * @returns {Object|null} - Geparseerde informatie of null
   */
  parseExtractionResponse(content) {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) return null;

      const data = JSON.parse(jsonMatch[0]);
      
      // Valideer alleen de essentiële velden
      if (!data.name || !data.currentRole || !data.company) {
        return null;
      }

      return {
        name: data.name.trim(),
        currentRole: data.currentRole.trim(),
        company: data.company.trim(),
        location: data.location?.trim() || null
      };

    } catch (error) {
      logger.error('Fout bij parsen extractie response:', error);
      return null;
    }
  }

  /**
   * Haalt statistieken op over API gebruik
   * @returns {Object} - Statistieken
   */
  getStats() {
    return {
      totalRequests: this.totalRequests,
      totalTokens: this.totalTokens,
      totalErrors: this.totalErrors,
      averageTokensPerRequest: this.totalRequests ? Math.round(this.totalTokens / this.totalRequests) : 0
    };
  }
}

module.exports = new OpenAIService(); 