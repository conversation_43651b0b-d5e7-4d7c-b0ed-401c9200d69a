require('dotenv').config();
const googleSearchService = require('../src/services/googleSearchService');
const logger = require('../src/services/logger');

async function testSearch() {
  try {
    logger.info('Start test zoekopdracht');

    // Configureer zoekparameters
    const searchParams = {
      jobTitles: 'Wagenparkbeheer',
      location: 'Alkmaar',
      maxResults: 5
    };

    // Voer zoekopdracht uit
    const results = await googleSearchService.searchProfiles(searchParams);

    // Toon resultaten
    logger.info(`Aantal gevonden profielen: ${results.length}`);
    
    results.forEach((profile, index) => {
      logger.info(`\nProfiel ${index + 1}:`);
      logger.info(`Naam: ${profile.name}`);
      logger.info(`Functie: ${profile.currentRole}`);
      logger.info(`Bedrijf: ${profile.company}`);
      logger.info(`Locatie: ${profile.location}`);
      logger.info(`LinkedIn: ${profile.linkedInUrl}`);
      logger.info(`Website: ${profile.website || 'Niet gevonden'}`);
      
      if (profile.enrichedData) {
        logger.info('Verrijkte data:');
        logger.info(JSON.stringify(profile.enrichedData, null, 2));
      }
    });

    // Toon OpenAI statistieken
    const stats = require('../src/services/openaiService').getStats();
    logger.info('\nOpenAI API Statistieken:');
    logger.info(`Totaal requests: ${stats.totalRequests}`);
    logger.info(`Totaal tokens: ${stats.totalTokens}`);
    logger.info(`Gemiddeld tokens per request: ${stats.averageTokensPerRequest}`);
    logger.info(`Totaal errors: ${stats.totalErrors}`);

  } catch (error) {
    logger.error('Fout bij test zoekopdracht:', error);
  }
}

// Voer test uit
testSearch(); 