require('dotenv').config();
const axios = require('axios');

// API-sleutel uit het .env bestand halen
const apiKey = process.env.GOOGLE_PLACES_API_KEY;

// Controleren of de API-sleutel bestaat
if (!apiKey) {
  console.error('Fout: GOOGLE_PLACES_API_KEY is niet ingesteld in het .env bestand');
  process.exit(1);
}

// Eenvoudige Places API-aanvraag uitvoeren
async function testPlacesAPI() {
  try {
    // Zoek naar een plaats (bijvoorbeeld restaurants in Amsterdam)
    const query = 'restaurants in Amsterdam';
    const url = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(query)}&key=${apiKey}`;
    
    console.log('API-verzoek verzenden...');
    const response = await axios.get(url);
    
    // Controleer de status van het antwoord
    if (response.data.status === 'OK') {
      console.log('✅ API-verbinding succesvol!');
      console.log(`Aantal resultaten: ${response.data.results.length}`);
      console.log('Eerste resultaat:');
      console.log(`- Naam: ${response.data.results[0].name}`);
      console.log(`- Adres: ${response.data.results[0].formatted_address}`);
      console.log(`- Rating: ${response.data.results[0].rating}`);
    } else {
      console.error(`❌ API-fout: ${response.data.status}`);
      console.error('Foutdetails:', response.data.error_message);
    }
  } catch (error) {
    console.error('❌ Er is een fout opgetreden bij het maken van de API-aanvraag:');
    console.error(error.message);
    if (error.response) {
      console.error('Statuscode:', error.response.status);
      console.error('Antwoordgegevens:', error.response.data);
    }
  }
}

// Test uitvoeren
testPlacesAPI();