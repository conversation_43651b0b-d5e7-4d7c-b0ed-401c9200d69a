require('dotenv').config();
const { mapPlacesOnlyToAirtableFields } = require('./services/airtableService');
const Airtable = require('airtable');

// Airtable configuratie
const accessToken = process.env.AIRTABLE_ACCESS_TOKEN;
const baseId = process.env.AIRTABLE_BASE_ID;
const tableName = process.env.AIRTABLE_TABLE_NAME || 'Places';

if (!accessToken || !baseId) {
  console.error('Fout: AIRTABLE_ACCESS_TOKEN en AIRTABLE_BASE_ID moeten worden ingesteld in het .env bestand');
  process.exit(1);
}

const base = new Airtable({ apiKey: accessToken }).base(baseId);

async function testImprovedMapping() {
  console.log('🧪 Test verbeterde mapping (email + straatnaam)...\n');

  // Test data - simuleer Places API response
  const mockPlaceDetails = {
    name: 'Test Dakdekker Verbeterd BV',
    formatted_address: 'Teststraat 123, Alkmaar, 1811 AB, Nederland',
    formatted_phone_number: '+31 72 123 4567',
    website: 'https://testdakdekker.nl',
    url: 'https://maps.google.com/?cid=12345678901234567890',
    place_id: 'ChIJtest123',
    rating: 4.5,
    user_ratings_total: 25,
    types: ['roofing_contractor', 'establishment']
  };

  // Test de mapping functie
  console.log('1. Test verbeterde mapping functie...');
  const mappedFields = mapPlacesOnlyToAirtableFields(mockPlaceDetails);
  
  console.log('✅ Gemapte velden:');
  Object.entries(mappedFields).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });

  // Controleer specifieke velden
  console.log('\n🔍 Controle specifieke velden:');
  console.log(`✅ searchpage_url: ${mappedFields.searchpage_url ? 'AANWEZIG' : 'ONTBREEKT'}`);
  console.log(`✅ address_street: ${mappedFields.address_street ? 'AANWEZIG' : 'ONTBREEKT'}`);
  console.log(`✅ email: ${mappedFields.email ? 'AANWEZIG (geschat)' : 'ONTBREEKT'}`);

  // Test schrijven naar Airtable
  if (process.argv.includes('--write-test')) {
    console.log('\n2. Test schrijven naar Airtable...');
    try {
      const createdRecord = await base(tableName).create(mappedFields);
      console.log('✅ Test record succesvol aangemaakt!');
      console.log(`   Record ID: ${createdRecord.getId()}`);
      console.log('   Velden in Airtable:');
      Object.entries(createdRecord.fields).forEach(([key, value]) => {
        console.log(`   ${key}: ${value}`);
      });
      
      // Controleer specifiek de nieuwe velden
      console.log('\n🎯 Controle nieuwe velden in Airtable:');
      console.log(`   searchpage_url: ${createdRecord.fields.searchpage_url || 'ONTBREEKT'}`);
      console.log(`   address_street: ${createdRecord.fields.address_street || 'ONTBREEKT'}`);
      console.log(`   email: ${createdRecord.fields.email || 'ONTBREEKT'}`);
      
    } catch (error) {
      console.error('❌ Fout bij schrijven naar Airtable:');
      console.error(`   ${error.message}`);
      if (error.statusCode) {
        console.error(`   Status code: ${error.statusCode}`);
        console.error(`   Details: ${JSON.stringify(error.error || {})}`);
      }
    }
  } else {
    console.log('\n💡 Gebruik --write-test om daadwerkelijk naar Airtable te schrijven');
  }
}

testImprovedMapping().catch(console.error);
