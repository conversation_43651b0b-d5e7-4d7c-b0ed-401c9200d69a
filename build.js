require('dotenv').config();
const { exec } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const archiver = require('archiver');

async function ensureDirectories() {
  // Maak eerst de basis directories
  const dirs = ['dist/config', 'dist/data', 'dist/logs'];
  
  for (const dir of dirs) {
    await fs.ensureDir(dir);
    // Voeg een .gitkeep toe zodat de directories in git blijven
    await fs.writeFile(
      path.join(dir, '.gitkeep'),
      '# Deze map wordt gebruikt door de applicatie'
    );
  }

  // Kopieer bestaande configuraties als die er zijn
  try {
    await fs.copy('config', 'dist/config', {
      filter: (src) => {
        // Kopieer alleen .json bestanden en directories
        return fs.statSync(src).isDirectory() || path.extname(src) === '.json';
      }
    });
  } catch (error) {
    console.log('<PERSON><PERSON> bestaande configuraties gevonden om te kopiëren');
  }
}

async function build() {
  try {
    // Onderdruk waarschuwingen tijdens build
    process.env.NODE_NO_WARNINGS = '1';
    // Zet productie mode
    process.env.NODE_ENV = 'production';
    
    console.log('🚀 Start build proces...');
    
    // 0. Controleer vereisten
    if (!process.env.WHOP_API_KEY) {
      console.error('❌ WHOP_API_KEY niet gevonden!');
      console.log('Zorg dat je .env bestand de WHOP_API_KEY bevat.');
      console.log('Huidige .env locatie:', path.resolve('.env'));
      console.log('Huidige directory:', process.cwd());
      process.exit(1);
    }

    console.log('✓ WHOP_API_KEY gevonden:', process.env.WHOP_API_KEY.substring(0, 6) + '...');

    // 1. Schoon dist directory
    console.log('\n1. Directory opschonen...');
    await fs.emptyDir('dist');
    
    // 2. API keys versleutelen
    console.log('\n2. API keys versleutelen...');
    await new Promise((resolve, reject) => {
      const encryptProcess = exec('node scripts/encrypt-api-key.js', (error) => {
        if (error) reject(error);
        else resolve();
      });

      // Log output
      encryptProcess.stdout.on('data', (data) => {
        console.log(data.toString().trim());
      });
      encryptProcess.stderr.on('data', (data) => {
        console.error(data.toString().trim());
      });
    });

    // Maak directories aan vóór het bouwen van de executable
    console.log('\n1. Directories voorbereiden...');
    await ensureDirectories();

    // 3. Bouw executable
    console.log('\n3. Executable bouwen...');
    await new Promise((resolve, reject) => {
      const pkgProcess = exec(
        'pkg . --targets node16-win-x64 --output dist/places-scraper.exe --options no-warnings', 
        (error) => {
          if (error) reject(error);
          else resolve();
        }
      );

      // Log pkg output
      pkgProcess.stdout.on('data', (data) => {
        console.log(data.toString());
      });
      pkgProcess.stderr.on('data', (data) => {
        console.error(data.toString());
      });
    });

    // 4. Kopieer benodigde bestanden
    console.log('\n4. Assets kopiëren...');
    await fs.copy('config', 'dist/config');
    await fs.copy('.env.template', 'dist/.env.template');
    await fs.copy('README.md', 'dist/README.md');
    await fs.ensureDir('dist/data');
    await fs.ensureDir('dist/logs');

    // 5. Maak ZIP bestand
    console.log('\n5. Release package maken...');
    const pkg = require('./package.json');
    const releasePath = path.join(__dirname, 'release');
    await fs.ensureDir(releasePath);
    
    const zipPath = path.join(releasePath, `places-scraper-v${pkg.version}.zip`);
    const output = fs.createWriteStream(zipPath);
    const archive = archiver('zip', { zlib: { level: 9 } });

    output.on('close', () => {
      console.log(`\n✨ Build succesvol afgerond!`);
      console.log(`📦 Release package: ${zipPath}`);
      console.log(`📊 Grootte: ${(archive.pointer() / 1024 / 1024).toFixed(2)} MB`);
    });

    archive.on('error', (err) => {
      throw err;
    });

    archive.pipe(output);
    archive.directory('dist', false);
    await archive.finalize();

  } catch (error) {
    console.error('\n❌ Build fout:', error);
    console.error('Details:', error.message);
    process.exit(1);
  }
}

build(); 