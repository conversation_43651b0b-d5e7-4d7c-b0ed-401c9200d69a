# Places API Scraper

Een flexibele tool voor het scrapen en verrijken van bedrijfsinformatie via Google Places API en Firecrawl.

## 🚀 Drie Scraping Modi

### 1. **Snelle Modus** (Alleen Places API)
- **Kosten**: ~€0.01 per bedrijf
- **Snelheid**: 1-3 seconden per bedrijf
- **Data**: Basis bedrijfsinfo (naam, adres, telefoon, website)
- **Gebruik**: Marktonderzoek, grote datasets

### 2. **Hybride Modus** (Places API + Beperkte Firecrawl) ⭐ NIEUW
- **Kosten**: ~€0.05-0.15 per bedrijf
- **Snelheid**: 15-30 seconden per bedrijf
- **Data**: Basis info + contactpersoon, email, functietitel, samenvatting
- **Gebruik**: Lead generatie met budget constraints

### 3. **Volledige Modus** (Places API + Volledige Firecrawl)
- **Kosten**: ~€0.10-0.50 per bedrijf
- **Snelheid**: 30-60 seconden per bedrijf
- **Data**: Complete bedrijfsprofielen met alle contactgegevens
- **Gebruik**: Sales prospecting, kwaliteit over kwantiteit

## 📊 Data Velden Vergelijking

| Veld | Snelle Modus | Hybride Modus | Volledige Modus |
|------|-------------|---------------|-----------------|
| Bedrijfsnaam | ✅ | ✅ | ✅ |
| Adres | ✅ | ✅ | ✅ |
| Telefoon | ✅ | ✅ | ✅ |
| Website | ✅ | ✅ | ✅ |
| **Contactpersoon naam** | ❌ | ✅ | ✅ |
| **Functietitel** | ❌ | ✅ | ✅ |
| **Email adres** | ❌ | ✅ | ✅ |
| **Bedrijfssamenvatting** | ❌ | ✅ | ✅ |
| Uitgebreide contactgegevens | ❌ | ❌ | ✅ |
| Departement info | ❌ | ❌ | ✅ |

## 🛠️ Installatie
1. Download en pak het ZIP bestand uit
2. Kopieer `.env.template` naar `.env`
3. Vul je API sleutels in in het `.env` bestand:
   - Google Places API key voor het ophalen van locaties
   - Airtable gegevens voor het opslaan van resultaten
   - Firecrawl API key voor website verrijking (optioneel)

## 🎯 Gebruik

### Interactieve Modus
```bash
node src/cli.js
```
Kies uit:
- Zoeken op locatie (grid-gebaseerd) - Volledige modus
- Zoeken op locatie (alleen Places API - snel) - Snelle modus
- **Zoeken op locatie (hybride - beperkte Firecrawl)** - Hybride modus ⭐
- Verwerken van URL-lijst uit bestand
- Zoeken op bedrijfsnamen
- Zoeken op functie en plaats

### Command Line Modus
```bash
# Snelle modus (alleen Places API)
node src/cli.js --no-enrich -c "52.3676,4.9041" -r 5 -k "restaurant"

# Hybride modus (Places + beperkte Firecrawl)
node src/cli.js --hybrid-enrich -c "52.3676,4.9041" -r 5 -k "restaurant"

# Volledige modus (Places + volledige Firecrawl)
node src/cli.js -c "52.3676,4.9041" -r 5 -k "restaurant"
```

## 🧪 Test Scripts
```bash
# Test snelle modus
node src/test-grid-fast.js

# Test hybride modus
node src/test-grid-hybrid.js

# Test hybride mapping
node src/test-hybrid-mapping.js

# Test volledige Firecrawl
node src/test-scraper-firecrawl-debug.js
```

## 💡 Wanneer Welke Modus?

### Gebruik **Snelle Modus** voor:
- Marktonderzoek met grote datasets
- Eerste verkenning van een markt
- Budget beperkt
- Tijd is kritiek

### Gebruik **Hybride Modus** voor: ⭐
- Lead generatie met beperkt budget
- Contactgegevens nodig maar niet alle details
- Balans tussen kosten en datakwaliteit
- Meeste praktische toepassingen

### Gebruik **Volledige Modus** voor:
- Sales prospecting met hoge conversie
- Complete bedrijfsprofielen nodig
- Budget geen probleem
- Kwaliteit belangrijker dan kwantiteit

## 📈 Performance Tips
- Start met hybride modus voor de beste balans
- Gebruik kleinere grid-cellen (500m) voor betere dekking
- Verhoog delay tussen requests bij rate limiting
- Monitor Firecrawl kosten via dashboard

## Support
Bij problemen:
- Email: <EMAIL>
- Website: www.jouwbedrijf.com/support