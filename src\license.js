const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios'); // We gebruiken axios voor de API calls
const os = require('os');
const { machineIdSync } = require('node-machine-id');

// Importeer de versleutelde API key
const encryptedKeys = require('./encrypted-keys');

class LicenseManager {
  constructor() {
    this.basePath = process.pkg ? path.dirname(process.execPath) : process.cwd();
    this.licenseFile = path.join(this.basePath, 'license.key');
    this.metaFile = path.join(this.basePath, 'license.meta.json');
    this.whopApiKey = this.decryptApiKey();
    this.whopApiUrl = 'https://api.whop.com/api/v2';
    this.machineId = this.getMachineId();
    
    // Verwijder de logging volledig
    // if (process.env.NODE_ENV !== 'production') {
    //   const maskedKey = this.whopApiKey ? 
    //     `${this.whopApiKey.substring(0, 6)}...${this.whopApiKey.substring(this.whopApiKey.length - 4)}` : 
    //     'niet gevonden';
    //   console.log(`Whop configuratie:`);
    //   console.log(`- API URL: ${this.whopApiUrl}`);
    //   console.log(`- API Key: ${maskedKey}`);
    // }
  }

  decryptApiKey() {
    try {
      const decipher = crypto.createDecipheriv(
        'aes-256-gcm',
        encryptedKeys.key,
        Buffer.from(encryptedKeys.iv, 'hex')
      );
      decipher.setAuthTag(Buffer.from(encryptedKeys.authTag, 'hex'));
      
      const decrypted = Buffer.concat([
        decipher.update(Buffer.from(encryptedKeys.encryptedKey, 'hex')),
        decipher.final()
      ]);
      
      return decrypted.toString('utf8');
    } catch (error) {
      console.error('Fout bij decryptie van API key');
      return null;
    }
  }

  // Genereer unieke machine ID
  getMachineId() {
    try {
      return machineIdSync(true);
    } catch (error) {
      // Fallback als machine-id niet beschikbaar is
      const networkInterfaces = os.networkInterfaces();
      const macAddresses = Object.values(networkInterfaces)
        .flat()
        .filter(details => details.mac && details.mac !== '00:00:00:00:00:00')
        .map(details => details.mac)
        .join('');
      
      return crypto
        .createHash('sha256')
        .update(macAddresses + os.hostname())
        .digest('hex');
    }
  }

  // Versleutel gevoelige data
  encrypt(text, key) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-gcm', Buffer.from(key), iv);
    const encrypted = Buffer.concat([cipher.update(text), cipher.final()]);
    const tag = cipher.getAuthTag();
    return {
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
      content: encrypted.toString('hex')
    };
  }

  // Ontsleutel versleutelde data
  decrypt(encrypted, key) {
    const decipher = crypto.createDecipheriv(
      'aes-256-gcm',
      Buffer.from(key),
      Buffer.from(encrypted.iv, 'hex')
    );
    decipher.setAuthTag(Buffer.from(encrypted.tag, 'hex'));
    const decrypted = Buffer.concat([
      decipher.update(Buffer.from(encrypted.content, 'hex')),
      decipher.final()
    ]);
    return decrypted.toString();
  }

  async validateLicense(licenseKey) {
    try {
      const url = `https://api.whop.com/api/v2/memberships/${licenseKey}/validate_license`;
      
      const metadata = {
        machine_id: this.machineId,
        hostname: os.hostname(),
        timestamp: new Date().toISOString()
      };

      try {
        const response = await axios.post(
          url,
          { metadata },
          {
            headers: {
              'Authorization': `Bearer ${this.whopApiKey}`,
              'Content-Type': 'application/json'
            }
          }
        );

        if (response.data.valid) {
          // Sla eerst de plain text licentie key op
          await this.saveLicense(licenseKey);

          // Maak de metadata aan
          const metaData = {
            licenseKey,
            machineId: this.machineId,
            validatedAt: new Date().toISOString(),
            expiresAt: response.data.expires_at
          };

          // Maak de encryptie key
          const encryptionKey = crypto
            .createHash('sha256')
            .update(this.machineId + licenseKey)
            .digest();

          // Versleutel en sla de metadata op
          const encryptedMeta = this.encrypt(JSON.stringify(metaData), encryptionKey);
          await this.saveMetadata(encryptedMeta);

          return true;
        }

        console.log('\n❌ Ongeldige licentie');
        return false;
      } catch (error) {
        if (error.response?.status === 400 && 
            error.response.data?.error?.message === 'Please reset your key to use on a new machine') {
          console.log('\n❌ Deze licentie is al in gebruik op een ander apparaat');
          console.log('\nOm deze licentie op dit apparaat te gebruiken:');
          console.log('1. Ga naar de Whop portal');
          console.log('2. Zoek deze licentie key');
          console.log('3. Klik op "Reset License"');
          console.log('4. Start deze applicatie opnieuw en voer de licentie key in');
          console.log('\nLet op: na het resetten werkt de licentie niet meer op het vorige apparaat.');
          
          await new Promise(resolve => setTimeout(resolve, 10000));
          throw new Error('Reset de licentie handmatig in de Whop portal');
        }
        
        // Andere error cases
        if (error.response?.status === 401) {
          throw new Error('API key heeft onvoldoende rechten. Controleer de memberships:validate permissie.');
        } else if (error.response?.status === 404) {
          throw new Error('Licentie niet gevonden');
        } else if (error.response?.status === 400) {
          throw new Error('Ongeldige licentie key formaat');
        }
        
        throw error;
      }
    } catch (error) {
      throw new Error(`Licentiefout: ${error.message}`);
    }
  }

  // Helper functie om licentie data op te slaan
  async saveLicenseData(licenseKey) {
    // Gebruik dezelfde encryptie methode
    const encryptionKey = crypto
      .createHash('sha256')
      .update(this.machineId + licenseKey)
      .digest();

    // Sla de licentie key op als plain text
    await this.saveLicense(licenseKey);

    // Versleutel en sla de metadata op
    const metaData = {
      licenseKey,
      machineId: this.machineId,
      validatedAt: new Date().toISOString()
    };

    const encryptedMeta = this.encrypt(JSON.stringify(metaData), encryptionKey);
    await this.saveMetadata(encryptedMeta);
  }

  async validateWithWhop(licenseKey) {
    try {
      const response = await axios.get(`${this.whopApiUrl}/licenses/validate`, {
        headers: {
          'Authorization': `Bearer ${this.whopApiKey}`,
          'Content-Type': 'application/json'
        },
        params: {
          license_key: licenseKey
        }
      });

      return {
        valid: response.data.valid,
        expires_at: response.data.expires_at
      };
    } catch (error) {
      throw error;
    }
  }

  async checkLicenseExists() {
    try {
      // Check of beide bestanden bestaan
      const licenseExists = await fs.access(this.licenseFile)
        .then(() => true)
        .catch(() => false);

      const metaExists = await fs.access(this.metaFile)
        .then(() => true)
        .catch(() => false);

      if (!licenseExists || !metaExists) {
        // Verberg deze logging in productie
        if (process.env.NODE_ENV !== 'production') {
          console.log('Bestanden niet gevonden:');
          console.log('- License file:', this.licenseFile, licenseExists ? '✓' : '✗');
          console.log('- Meta file:', this.metaFile, metaExists ? '✓' : '✗');
        }
        return false;
      }

      // Lees en valideer de metadata
      const metadata = await this.readMetadata();
      return this.validateMachineId(metadata);
    } catch (error) {
      console.error('Fout bij checken licentie:', error);
      return false;
    }
  }

  isValidOffline(metadata) {
    try {
      if (!metadata) return false;

      // Controleer offline validatie periode (7 dagen)
      const validationAge = Date.now() - new Date(metadata.validatedAt).getTime();
      const maxOfflineAge = 7 * 24 * 60 * 60 * 1000; // 7 dagen in ms

      if (validationAge > maxOfflineAge) {
        console.log('Licentie is te oud voor offline validatie');
        return false;
      }

      // Controleer machine ID
      if (!this.validateMachineId(metadata)) {
        console.log('Machine ID komt niet overeen');
        return false;
      }

      // Controleer expiratie
      if (metadata.expiresAt) {
        const expiresAt = new Date(metadata.expiresAt).getTime();
        if (Date.now() > expiresAt) {
          console.log('Licentie is verlopen');
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Fout bij offline validatie:', error);
      return false;
    }
  }

  validateMachineId(metadata) {
    try {
      if (!metadata || !metadata.machineId) {
        if (process.env.NODE_ENV !== 'production') {
          console.log('Geen machine ID gevonden in metadata');
        }
        return false;
      }
      
      const currentMachineId = this.getMachineId();
      const matches = metadata.machineId === currentMachineId;
      
      if (!matches && process.env.NODE_ENV !== 'production') {
        console.log('Machine ID mismatch:');
        console.log('Opgeslagen:', metadata.machineId);
        console.log('Huidig:', currentMachineId);
      }
      
      return matches;
    } catch (error) {
      if (process.env.NODE_ENV !== 'production') {
        console.error('Fout bij machine ID validatie:', error);
      }
      return false;
    }
  }

  async verifyLicenseOnline(licenseKey) {
    try {
      const response = await axios.get(`${this.whopApiUrl}/licenses/validate`, {
        headers: {
          'Authorization': `Bearer ${this.whopApiKey}`,
          'Content-Type': 'application/json'
        },
        params: {
          license_key: licenseKey
        }
      });

      if (response.data.valid) {
        // Update metadata bij succesvolle verificatie
        const metadata = {
          licenseKey,
          expiresAt: response.data.expires_at,
          validatedAt: new Date().toISOString()
        };
        await this.saveMetadata(metadata);
        return true;
      }
      return false;
    } catch (error) {
      // Bij netwerk error, val terug op offline check
      const metadata = await this.readMetadata();
      return this.isValidOffline(metadata);
    }
  }

  async readMetadata() {
    try {
      // Lees eerst de licentie key
      const licenseContent = await fs.readFile(this.licenseFile, 'utf8');
      const licenseKey = licenseContent.trim(); // Veronderstel dat het een plain text key is

      // Lees de versleutelde metadata
      const encryptedMetaStr = await fs.readFile(this.metaFile, 'utf8');
      const encryptedMeta = JSON.parse(encryptedMetaStr);

      // Gebruik dezelfde encryptie methode als bij het opslaan
      const encryptionKey = crypto
        .createHash('sha256')
        .update(this.machineId + licenseKey)
        .digest();

      // Gebruik de decrypt helper functie die we al hebben
      const decryptedStr = this.decrypt(encryptedMeta, encryptionKey);
      return JSON.parse(decryptedStr);

    } catch (error) {
      if (process.env.NODE_ENV !== 'production') {
        console.error('Fout bij lezen metadata:', error);
      }
      return null;
    }
  }

  async saveMetadata(metadata) {
    await fs.writeFile(this.metaFile, JSON.stringify(metadata, null, 2));
  }

  async saveLicense(licenseKey) {
    // Sla de licentie key op als plain text
    await fs.writeFile(this.licenseFile, licenseKey);
  }

  generateLicenseKey(clientId) {
    // Genereer een unieke licentie sleutel voor een klant
    const hash = crypto.createHash('sha256');
    hash.update(clientId + this.secretKey);
    return hash.digest('hex').substring(0, 24);
  }
}

module.exports = LicenseManager; 