/**
 * Firecrawl API Client
 * Biedt methodes voor het interageren met de Firecrawl API voor data extractie.
 */

require('dotenv').config();
const axios = require('axios');
const logger = require('./logger');

const FIRE_CRAWL_API_KEY = process.env.FIRE_CRAWL_API_KEY;
// Basis URL voor de Firecrawl API
const FIRE_CRAWL_BASE_URL = 'https://api.firecrawl.dev/v1';
// Endpoints
const EXTRACT_ENDPOINT = `${FIRE_CRAWL_BASE_URL}/extract`;
const EXTRACT_STATUS_ENDPOINT = `${FIRE_CRAWL_BASE_URL}/extract/`;

// Controleren of API key is ingesteld
if (!FIRE_CRAWL_API_KEY) {
  logger.error('⚠️ FIRE_CRAWL_API_KEY niet gevonden in .env bestand. Website verrijking wordt overgeslagen.');
} else {
  logger.info(`[Firecrawl] API Key gevonden: ${FIRE_CRAWL_API_KEY.substring(0, 5)}...${FIRE_CRAWL_API_KEY.substring(FIRE_CRAWL_API_KEY.length - 4)}`);
}

// Definieer het JSON schema voor de te extraheren data
// Dit helpt de LLM om gestructureerde output te geven
const extractionSchema = {
  type: 'object',
  properties: {
    contactPerson: {
      type: 'object',
      properties: {
        name: { type: 'string', description: 'Volledige naam van de contactpersoon voor hiring/recruitment' },
        title: { type: 'string', description: 'Functietitel (bijv. HR Manager, Recruiter)' },
        department: { type: 'string', description: 'Afdeling (bijv. HR, Personeelszaken)' },
        email: { type: 'string', description: 'E-mailadres van de contactpersoon' },
        phone: { type: 'string', description: 'Telefoonnummer van de contactpersoon' }
      },
      description: 'Details van de persoon die het meest waarschijnlijk verantwoordelijk is voor aannemen/recruitment.'
    },
    companySummary: {
      type: 'string',
      description: 'Een korte samenvatting van de hoofdactiviteiten of missie van het bedrijf.'
    },
    services: {
      type: 'object',
      properties: {
        hasVMware: { type: 'boolean', description: 'Biedt het bedrijf VMware-gerelateerde diensten aan?' },
        hasCloudComputing: { type: 'boolean', description: 'Biedt het bedrijf Cloud Computing diensten aan?' },
        hasCloudSolutions: { type: 'boolean', description: 'Biedt het bedrijf Cloud Solutions aan?' },
        hasVirtualization: { type: 'boolean', description: 'Biedt het bedrijf Virtualization diensten aan?' }
      },
      description: 'Geeft aan of specifieke technologische diensten worden aangeboden.'
    }
  },
  required: ['companySummary', 'services'] // Contactpersoon is optioneel
};

// De prompt die de LLM instrueert wat te doen
const extractionPrompt = `
Analyseer de inhoud van deze website en extraheer de volgende informatie:

1. Bedrijfssamenvatting: Geef een korte beschrijving (1-3 zinnen) van wat het bedrijf doet.

2. Contactpersoon voor werving: Als je een persoon vindt die verantwoordelijk is voor HR, recruitment of het aannemen van personeel, geef dan hun naam, functie, afdeling, e-mail en telefoonnummer. Als je geen specifieke persoon vindt, laat dit veld dan leeg.

3. Diensten: Geef aan of het bedrijf de volgende diensten aanbiedt (true/false):
   - VMware
   - Cloud Computing
   - Cloud Solutions
   - Virtualization

Baseer je antwoord alleen op de informatie die je op de website vindt. Geef het antwoord in het Nederlands.
`;

class FirecrawlClient {
  constructor(options = {}) {
    this.options = {
      apiKey: FIRE_CRAWL_API_KEY,
      baseUrl: FIRE_CRAWL_BASE_URL,
      pollingInterval: 60000, // 60 seconden polling interval
      maxPollingAttempts: 30, // maximaal 30 minuten wachten
      ...options
    };

    // Valideer API key bij initialisatie
    if (!this.options.apiKey) {
      logger.error('Firecrawl API key ontbreekt in constructor');
    } else {
      logger.info('Firecrawl client geïnitialiseerd met API key', {
        keyPrefix: this.options.apiKey.substring(0, 5),
        keySuffix: this.options.apiKey.substring(this.options.apiKey.length - 4)
      });
    }
  }

  /**
   * Verrijkt bedrijfsdata met contactgegevens via Firecrawl
   * @param {string} url - Bedrijfswebsite URL
   * @param {Object} [personInfo] - Informatie over de persoon
   * @param {string} [personInfo.name] - Naam van de persoon
   * @param {string} [personInfo.role] - Functie van de persoon
   * @returns {Promise<Object|null>} - Verrijkte data of null
   */
  async enrichCompanyData(url, personInfo = null) {
    if (!this.options.apiKey) {
      logger.error('Firecrawl API key ontbreekt bij enrichCompanyData');
      throw new Error('Firecrawl API key ontbreekt');
    }

    // Zorg dat de URL eindigt met /* voor wildcard crawling
    const wildcardUrl = url.endsWith('/*') ? url : url + '/*';

    // Bouw de prompt op basis van beschikbare persoonlijke informatie
    let prompt = 'Zoek op de bedrijfswebsite naar ';
    if (personInfo?.name && personInfo?.role) {
      prompt += `contactgegevens (email en telefoonnummer) van ${personInfo.name} (functie: ${personInfo.role}). `;
      prompt += 'Als deze niet gevonden worden, ';
    }
    prompt += 'geef dan de algemene contactgegevens (email en telefoonnummer) van het bedrijf.';
    prompt += ' Geef daarnaast een samenvatting maximaal 300 woorden van wat het bedrijf doet c.q. wat haar service is, gebaseerd op de website. Geef het antwoord in het Nederlands.';

    logger.info('Start Firecrawl extractie', {
      url: wildcardUrl,
      personInfo,
      prompt
    });

    // Start de extractie job
    const response = await axios.post(
      `${this.options.baseUrl}/extract`,
      {
        urls: [wildcardUrl],
        prompt: prompt,
        schema: this.getExtractionSchema()
      },
      {
        headers: {
          'Authorization': `Bearer ${this.options.apiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.data?.id) {
      throw new Error('Geen job ID ontvangen van Firecrawl');
    }

    logger.info('Firecrawl extractie job gestart', {
      jobId: response.data.id,
      url: wildcardUrl
    });

    // Start polling voor resultaten
    const result = await this.pollExtractStatus(response.data.id);
    
    if (result) {
      logger.info('Firecrawl extractie voltooid', {
        url: wildcardUrl,
        foundContactPerson: !!result.contact_person?.email || !!result.contact_person?.phone,
        foundCompanyContact: !!result.company_contact?.email || !!result.company_contact?.phone,
        contactPerson: result.contact_person ? {
          name: result.contact_person.name,
          role: result.contact_person.role,
          hasEmail: !!result.contact_person.email,
          hasPhone: !!result.contact_person.phone
        } : null,
        companyContact: result.company_contact ? {
          hasEmail: !!result.company_contact.email,
          hasPhone: !!result.company_contact.phone,
          hasAddress: !!result.company_contact.address
        } : null
      });
    }

    return result;
  }

  /**
   * Pollt de status van een extractie job
   * @param {string} jobId - ID van de extractie job
   * @returns {Promise<Object|null>} - Extractie resultaten of null
   */
  async pollExtractStatus(jobId) {
    let attempts = 0;
    
    while (attempts < this.options.maxPollingAttempts) {
      try {
        const response = await axios.get(
          `${this.options.baseUrl}/extract/${jobId}`,
          {
            headers: {
              'Authorization': `Bearer ${this.options.apiKey}`
            }
          }
        );

        const { status, data } = response.data;

        if (status === 'completed') {
          logger.info('Firecrawl job voltooid', {
            jobId,
            status,
            hasData: !!data
          });
          return data;
        } else if (status === 'failed' || status === 'cancelled') {
          logger.warn('Firecrawl job mislukt of geannuleerd', {
            jobId,
            status,
            attempt: attempts + 1
          });
          return null;
        }

        if (attempts % 5 === 0) { // Log elke 5 pogingen
          logger.info('Firecrawl job nog in verwerking', {
            jobId,
            status,
            attempt: attempts + 1,
            maxAttempts: this.options.maxPollingAttempts,
            nextCheckIn: `${this.options.pollingInterval / 1000} seconden`
          });
        }

        // Wacht voor de volgende polling poging
        await new Promise(resolve => setTimeout(resolve, this.options.pollingInterval));
        attempts++;

      } catch (error) {
        logger.error('Fout bij polling extractie job', {
          jobId,
          attempt: attempts + 1,
          error: error.message
        });
        return null;
      }
    }

    logger.warn('Timeout bij polling extractie job', {
      jobId,
      attempts,
      maxAttempts: this.options.maxPollingAttempts,
      totalWaitTime: `${(this.options.maxPollingAttempts * this.options.pollingInterval) / 1000} seconden`
    });
    return null;
  }

  /**
   * Start meerdere extractie jobs parallel
   * @param {Array<{url: string, personInfo: Object}>} jobs - Array van jobs met URL en persoonlijke info
   * @returns {Promise<Array<{jobId: string, url: string, personInfo: Object}>>} - Array van job IDs met context
   */
  async startParallelExtractions(jobs) {
    logger.info('Start parallelle Firecrawl extracties', {
      totalJobs: jobs.length,
      urls: jobs.map(j => j.url.endsWith('/*') ? j.url : j.url + '/*')
    });

    const extractionPromises = jobs.map(async ({ url, personInfo }) => {
      const wildcardUrl = url.endsWith('/*') ? url : url + '/*';
      try {
        const response = await axios.post(
          `${this.options.baseUrl}/extract`,
          {
            urls: [wildcardUrl],
            prompt: this.buildExtractionPrompt(personInfo),
            schema: this.getExtractionSchema()
          },
          {
            headers: {
              'Authorization': `Bearer ${this.options.apiKey}`,
              'Content-Type': 'application/json'
            }
          }
        );

        logger.info('Parallelle Firecrawl job gestart', {
          url: wildcardUrl,
          jobId: response.data?.id,
          personInfo: personInfo ? {
            name: personInfo.name,
            role: personInfo.role
          } : null
        });

        return {
          jobId: response.data?.id,
          url: wildcardUrl,
          personInfo
        };
      } catch (error) {
        logger.error('Fout bij starten parallelle extractie', {
          url: wildcardUrl,
          error: error.message,
          personInfo: personInfo ? {
            name: personInfo.name,
            role: personInfo.role
          } : null
        });
                return null;
              }
    });

    const results = await Promise.all(extractionPromises);
    const successfulJobs = results.filter(result => result !== null);
    
    logger.info('Parallelle Firecrawl jobs gestart', {
      totalJobs: jobs.length,
      successfulJobs: successfulJobs.length,
      failedJobs: jobs.length - successfulJobs.length
    });

    return successfulJobs;
  }

  /**
   * Bouwt de extractie prompt op basis van persoonlijke informatie
   * @param {Object} personInfo - Informatie over de persoon
   * @returns {string} - Prompt voor extractie
   */
  buildExtractionPrompt(personInfo) {
    let prompt = 'Zoek op de bedrijfswebsite naar ';
    if (personInfo?.name && personInfo?.role) {
      prompt += `contactgegevens (email en telefoonnummer) van ${personInfo.name} (functie: ${personInfo.role}). `;
      prompt += 'Als deze niet gevonden worden, ';
    }
    prompt += 'geef dan de algemene contactgegevens (email en telefoonnummer) van het bedrijf.';
    return prompt;
  }

  /**
   * Geeft het schema voor de extractie
   * @returns {Object} - JSON Schema voor extractie
   */
  getExtractionSchema() {
    return {
      type: 'object',
      properties: {
        contact_person: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            role: { type: 'string' },
            email: { type: 'string' },
            phone: { type: 'string' }
          }
        },
        company_contact: {
          type: 'object',
          properties: {
            email: { type: 'string' },
            phone: { type: 'string' },
            address: { type: 'string' }
          }
        },
        summary: {
          type: 'string',
          description: 'Korte samenvatting van het bedrijf (max 3 zinnen)'
        }
      }
    };
  }
}

module.exports = new FirecrawlClient(); 