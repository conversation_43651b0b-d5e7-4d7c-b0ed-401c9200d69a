require('dotenv').config();
const ApifyScraper = require('./apifyScraper');

async function apifyAlkmaarHybridScraping() {
  console.log('🏗️ Alkmaar Bouw & Renovatie Scraping - Apify Hybride Modus\n');

  // Alkmaar locatie voor Apify
  const location = 'Alkmaar, Netherlands';
  
  // Alle bouwgerelateerde zoektermen (zelfde als originele alkmaar-scraping.js)
  const keywords = [
    'Dakdek<PERSON>',
    'Timmerman', 
    'Meubelmaker',
    'Houtbewerker',
    'Carport- en pergolabouwer',
    '<PERSON>ukenrenovatie',
    'Badkamerrenovatie',
    '<PERSON><PERSON><PERSON>',
    'Stukadoor',
    'Metselaar',
    'Tegelze<PERSON>',
    'Bestratingsaannemer',
    'Betonaannemer',
    'Sloopaannemer',
    'Terras- en verandabouwer',
    'Steigerbouwer',
    'Tuinhuisbouwer',
    'Logh<PERSON>sbouwer',
    'Balustrade- en railingaannemer',
    'He<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>aanne<PERSON>',
    'Interieurbouwer',
    'Plaatwerker',
    'Staalconstructeur',
    '<PERSON><PERSON>',
    'Elektricien',
    'Loodgieter',
    'Gootreiniger',
    'Afvoerspecialist',
    'Verwarmingsmonteur',
    'HVAC-monteur',
    'Parketlegger',
    'Vloerenspecialist',
    'Marmeraannemer',
    'Hovenier',
    'Graafmachinist',
    'Booraannemer',
    'Smid',
    'Glaszetter',
    'Dubbelglaszetter',
    'Ramenwasser',
    'Droogbouwaannemer',
    'Klusjesman',
    'Prefabbouwer',
    'Straatmaker'
  ];

  console.log('📋 Apify Hybride Scraping configuratie:');
  console.log(`📍 Locatie: ${location}`);
  console.log(`🔑 Zoektermen: ${keywords.length} termen`);
  console.log(`🔄 Modus: Apify hybride scraping (Apify API + beperkte Firecrawl)`);
  console.log(`📊 Max plaatsen per zoekterm: 50`);
  console.log(`⏱️ Geschatte duur: 3-5 uur (vanwege Firecrawl verrijking)\n`);

  // Toon alle zoektermen
  console.log('🔍 Zoektermen:');
  keywords.forEach((keyword, index) => {
    console.log(`${(index + 1).toString().padStart(2, ' ')}. ${keyword}`);
  });
  console.log('');

  // Configureer scraper voor hybride Apify modus
  const scraper = new ApifyScraper({
    delayBetweenRequests: 3000, // Langzamer vanwege Firecrawl calls
    maxPlacesPerSearch: 50, // Max plaatsen per zoekterm
    enableFirecrawlEnrichment: true, // Firecrawl ingeschakeld
    firecrawlMode: 'limited', // Hybride modus - alleen specifieke velden
    logLevel: 'info',
    language: 'nl',
    website: 'onlyPlacesWithWebsite', // Alleen plaatsen met website voor Firecrawl
    searchMatching: 'all',
    skipClosedPlaces: false
  });

  try {
    console.log('🚀 Starten van Alkmaar Apify hybride scraping...\n');
    
    const startTime = Date.now();
    
    await scraper.run({
      location: location,
      keywords: keywords
    });

    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000 / 60); // minuten
    
    console.log('\n🎉 Alkmaar Apify hybride scraping voltooid!');
    console.log(`⏱️ Totale duur: ${duration} minuten`);
    console.log('📊 Check je Airtable voor alle verzamelde data');
    console.log('\n💡 Hybride modus voordelen:');
    console.log('- Meer contactgegevens dan alleen Apify data');
    console.log('- Bedrijfssamenvatting en contactpersoon informatie');
    console.log('- Ideaal voor lead generatie met uitgebreide data');
    
  } catch (error) {
    console.error('\n❌ Fout tijdens Alkmaar Apify hybride scraping:', error.message);
    console.error(error.stack);
  }
}

// Voer scraping uit
if (require.main === module) {
  apifyAlkmaarHybridScraping();
}

module.exports = apifyAlkmaarHybridScraping;
