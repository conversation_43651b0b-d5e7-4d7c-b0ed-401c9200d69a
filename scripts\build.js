const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const pkg = require('../package.json');

async function build() {
  try {
    console.log('🔨 Start build proces...');
    
    // Lees huidige versie
    const version = pkg.version;
    console.log(`📦 Building versie ${version}`);
    
    // Maak output pad met versienummer
    const outputPath = path.join('dist', `places-scraper-v${version}.exe`);
    
    // Voer pkg commando uit met alleen --output
    console.log('\nBuilding executable...');
    await execCommand(`pkg . --output ${outputPath}`);
    
    // Kopieer benodigde bestanden en mappen
    console.log('\nKopiëren van ondersteunende bestanden...');
    
    // Maak benodigde directories
    const directories = ['config', 'data', 'logs'];
    for (const dir of directories) {
      const dirPath = path.join('dist', dir);
      await fs.mkdir(dirPath, { recursive: true });
      console.log(`📁 Directory aangemaakt: ${dir}/`);
      
      // Voeg .gitkeep toe om lege mappen te behouden
      await fs.writeFile(
        path.join(dirPath, '.gitkeep'),
        '# Deze map wordt gebruikt door de applicatie'
      );
    }
    
    // Kopieer template bestanden
    const filesToCopy = [
      {
        src: '.env.template',
        dest: '.env.template'
      },
      {
        src: 'README.md',
        dest: 'README.md'
      }
    ];
    
    for (const file of filesToCopy) {
      try {
        await fs.copyFile(
          file.src,
          path.join('dist', file.dest)
        );
        console.log(`📄 Bestand gekopieerd: ${file.dest}`);
      } catch (error) {
        console.warn(`⚠️ Kon bestand niet kopiëren: ${file.src}`);
      }
    }
    
    console.log(`\n✅ Build succesvol afgerond!`);
    console.log(`📁 Executable: ${outputPath}`);
    console.log('\nBevat:');
    console.log('- places-scraper.exe (applicatie)');
    console.log('- .env.template (configuratie template)');
    console.log('- README.md (instructies)');
    console.log('- config/ (grid configuraties)');
    console.log('- data/ (output data)');
    console.log('- logs/ (logbestanden)');
    
  } catch (error) {
    console.error('❌ Build fout:', error);
    process.exit(1);
  }
}

function execCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error('Build output:', stdout);
        console.error('Build errors:', stderr);
        reject(error);
        return;
      }
      console.log(stdout);
      resolve();
    });
  });
}

build(); 