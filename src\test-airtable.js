require('dotenv').config();
const Airtable = require('airtable');

// Airtable configuratie
const accessToken = process.env.AIRTABLE_ACCESS_TOKEN;
const baseId = process.env.AIRTABLE_BASE_ID;
const tableName = process.env.AIRTABLE_TABLE_NAME || 'Places';
const viewName = process.env.AIRTABLE_VIEW_NAME || 'Grid view';

// Controleren of de vereiste omgevingsvariabelen zijn ingesteld
if (!accessToken || !baseId) {
  console.error('Fout: AIRTABLE_ACCESS_TOKEN en AIRTABLE_BASE_ID moeten worden ingesteld in het .env bestand');
  process.exit(1);
}

// Airtable basis initialiseren met access token
const base = new Airtable({ apiKey: accessToken }).base(baseId);

// Test Airtable verbinding
async function testAirtableConnection() {
  try {
    console.log('Verbinding maken met Airtable...');
    console.log(`Base ID: ${baseId}`);
    console.log(`Tabelnaam: ${tableName}`);
    console.log(`View naam: ${viewName}`);
    
    // Probeer de eerste 3 records uit de gespecificeerde tabel te halen
    const records = await base(tableName).select({
      maxRecords: 3,
      view: viewName
    }).firstPage();
    
    console.log('\n✅ Airtable-verbinding succesvol!');
    console.log(`Aantal opgehaalde records: ${records.length}`);
    
    // Toon beschikbare veldnamen (belangrijk voor debugging)
    console.log('\nBeschikbare veldnamen in Airtable tabel (exacte spelling):');
    if (records.length > 0) {
      const fieldNames = Object.keys(records[0].fields);
      fieldNames.forEach(field => console.log(`- "${field}"`));
    } else {
      console.log('Kon geen veldnamen ophalen omdat er geen records zijn.');
    }
    
    // Maak een testrecord met minimale velden
    const createRecord = process.argv.includes('--create-record');
    if (createRecord) {
      console.log('\nTestrecord aanmaken...');
      try {
        // Alleen velden die handmatig kunnen worden ingesteld (geen berekende velden)
        const createdRecord = await base(tableName).create({
          Name: 'Test Locatie',
          'Place ID': 'test123',
          Address: 'Teststraat 123, 1234 AB Amsterdam',
          Latitude: 52.3676,
          Longitude: 4.9041,
          Rating: 4.5,
          'Total Ratings': 10
          // Geen 'Created At' - dit is een berekend veld dat Airtable automatisch invult
        });
        console.log('✅ Testrecord aangemaakt met ID:', createdRecord.getId());
        console.log('Velden in aangemaakt record:');
        console.log(createdRecord.fields);
      } catch (error) {
        console.error('❌ Kon geen testrecord aanmaken:');
        console.error(error.message);
        if (error.statusCode) {
          console.error(`Status code: ${error.statusCode}`);
          console.error(`Foutdetails: ${JSON.stringify(error.error || {})}`);
        }
      }
    } else {
      console.log('\nGebruik "npm run test-airtable-write" om een testrecord aan te maken');
    }
    
  } catch (error) {
    console.error('❌ Er is een fout opgetreden bij het verbinden met Airtable:');
    console.error(error.message);
    
    if (error.statusCode) {
      console.error(`Status code: ${error.statusCode}`);
      console.error(`Foutdetails: ${JSON.stringify(error.error || {})}`);
    }
  }
}

// Voer de test uit
testAirtableConnection();