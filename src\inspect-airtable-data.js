require('dotenv').config();
const Airtable = require('airtable');

// Airtable configuratie
const accessToken = process.env.AIRTABLE_ACCESS_TOKEN;
const baseId = process.env.AIRTABLE_BASE_ID;
const tableName = process.env.AIRTABLE_TABLE_NAME || 'Places';

if (!accessToken || !baseId) {
  console.error('Fout: AIRTABLE_ACCESS_TOKEN en AIRTABLE_BASE_ID moeten worden ingesteld in het .env bestand');
  process.exit(1);
}

const base = new Airtable({ apiKey: accessToken }).base(baseId);

async function inspectAirtableData() {
  try {
    console.log('🔍 Inspecteer bestaande Airtable data...\n');
    
    // Haal alle records op
    const records = await base(tableName).select({
      maxRecords: 10
    }).firstPage();
    
    console.log(`Gevonden ${records.length} records:\n`);
    
    records.forEach((record, index) => {
      console.log(`--- Record ${index + 1} (ID: ${record.getId()}) ---`);
      Object.entries(record.fields).forEach(([key, value]) => {
        console.log(`${key}: ${value}`);
      });
      console.log('');
    });
    
    // Analyseer scrape_source waarden
    const scrapeSources = records
      .map(record => record.fields.scrape_source)
      .filter(source => source)
      .filter((source, index, arr) => arr.indexOf(source) === index); // unique values
    
    console.log('🎯 Bestaande scrape_source waarden:');
    scrapeSources.forEach(source => console.log(`- "${source}"`));
    
    if (scrapeSources.length === 0) {
      console.log('- (geen waarden gevonden)');
    }
    
  } catch (error) {
    console.error('❌ Fout bij inspecteren van data:');
    console.error(error.message);
    if (error.statusCode) {
      console.error(`Status code: ${error.statusCode}`);
    }
  }
}

inspectAirtableData();
