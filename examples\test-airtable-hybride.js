require('dotenv').config();
const { addProfileToAirtable } = require('../src/services/airtableService');
const Airtable = require('airtable');

const accessToken = process.env.AIRTABLE_ACCESS_TOKEN;
const baseId = process.env.AIRTABLE_BASE_ID;
const tableName = process.env.AIRTABLE_TABLE_NAME || 'Places';

const base = new Airtable({ apiKey: accessToken }).base(baseId);

async function testHybrideAirtable() {
  // Test 1: Verrijkt profiel (zoals uit Firecrawl/LinkedIn)
  const enrichedProfile = {
    company: 'Testbedrijf BV',
    name: '<PERSON>',
    website: 'https://testbedrijf.nl',
    linkedInUrl: 'https://linkedin.com/in/janjansen',
    enrichedData: {
      contact_person: {
        name: '<PERSON>',
        role: 'Directeur',
        email: '<EMAIL>',
        phone: '0612345678'
      },
      company_contact: {
        email: '<EMAIL>',
        phone: '020-1234567',
        address: 'Teststraat 1, 1234 AB Amsterdam'
      },
      summary: 'Testbedrijf BV is een fictief bedrijf voor testdoeleinden.'
    }
  };

  // Pas Airtable service aan om ook 'URL' te vullen met website of LinkedIn
  enrichedProfile.url = enrichedProfile.website || enrichedProfile.linkedInUrl || '';

  console.log('Test: verrijkt profiel via addProfileToAirtable...');
  await addProfileToAirtable(enrichedProfile);

  // Test 2: Standaard Google Places record (oude structuur, Nederlandse velden)
  const fields = {
    'Naam': 'Test Restaurant',
    'Adres': 'Etenstraat 5, 5678 CD Utrecht',
    'Telefoon': '030-9876543',
    'URL': 'https://testrestaurant.nl'
  };

  console.log('Test: standaard Google Places record via directe Airtable API...');
  await base(tableName).create([{ fields }], { typecast: true });

  console.log('✅ Beide tests uitgevoerd. Controleer Airtable voor resultaat.');
}

testHybrideAirtable().catch(err => {
  console.error('Testscript fout:', err);
}); 