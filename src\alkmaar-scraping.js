require('dotenv').config();
const PlacesScraper = require('./scraper');

async function alkmaarscraping() {
  console.log('🏗️ Alkmaar Bouw & Renovatie Scraping - Snelle Modus\n');

  // Alkmaar coördinaten
  const center = { lat: 52.6317, lng: 4.7516 };
  const radiusKm = 30;
  
  // Alle bouwgerelateerde zoektermen
  const keywords = [
    'Dakdek<PERSON>',
    'Timmerman', 
    'Meubelmaker',
    'Houtbewerker',
    'Carport- en pergolabouwer',
    'Keukenrenovatie',
    'Badkamerrenovatie',
    '<PERSON><PERSON><PERSON>',
    'Stukadoor',
    'Metselaar',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>rating<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    'Sloopaannemer',
    'Terras- en verandabouwer',
    'Steigerbouwer',
    'Tuinhuisbouwer',
    'Loghuisbouwer',
    'Balustrade- en railingaannemer',
    '<PERSON><PERSON><PERSON><PERSON>',
    'Isola<PERSON>aan<PERSON><PERSON>',
    'Interieurbouwer',
    'Plaatwerker',
    '<PERSON><PERSON>constructeur',
    '<PERSON><PERSON>',
    'Elektricien',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>ot<PERSON><PERSON><PERSON>',
    'Afvoerspecialist',
    'Verwarmingsmonteur',
    'HVAC-monteur',
    'Parketlegger',
    'Vloerenspecialist',
    'Marmeraannemer',
    'Hovenier',
    'Graafmachinist',
    'Booraannemer',
    'Smid',
    'Glaszetter',
    'Dubbelglaszetter',
    'Ramenwasser',
    'Droogbouwaannemer',
    'Klusjesman',
    'Prefabbouwer',
    'Straatmaker'
  ];

  console.log('📋 Scraping configuratie:');
  console.log(`📍 Locatie: Alkmaar (${center.lat}, ${center.lng})`);
  console.log(`⭕ Radius: ${radiusKm} km`);
  console.log(`🔑 Zoektermen: ${keywords.length} termen`);
  console.log(`🚀 Modus: Snelle scraping (alleen Places API)`);
  console.log(`📊 Grid cel grootte: 1000m (optimaal voor grote radius)`);
  console.log(`⏱️ Geschatte duur: 2-4 uur\n`);

  // Toon alle zoektermen
  console.log('🔍 Zoektermen:');
  keywords.forEach((keyword, index) => {
    console.log(`${(index + 1).toString().padStart(2, ' ')}. ${keyword}`);
  });
  console.log('');

  // Configureer scraper voor snelle modus
  const scraper = new PlacesScraper({
    delayBetweenRequests: 300, // Iets langzamer voor stabiliteit bij grote operatie
    enableFirecrawlEnrichment: false, // Snelle modus
    logLevel: 'info',
    maxPlacesPerGrid: 60 // Standaard limiet
  });

  try {
    console.log('🚀 Starten van Alkmaar scraping...\n');
    
    const startTime = Date.now();
    
    await scraper.run({
      center: center,
      radiusKm: radiusKm,
      keywords: keywords,
      gridSizeM: 1000 // Grotere grid voor efficiëntie bij grote radius
    });

    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000 / 60); // minuten
    
    console.log('\n🎉 Alkmaar scraping voltooid!');
    console.log(`⏱️ Totale duur: ${duration} minuten`);
    console.log('📊 Check je Airtable voor alle verzamelde data');
    
  } catch (error) {
    console.error('\n❌ Fout tijdens Alkmaar scraping:', error.message);
    console.error(error.stack);
  }
}

// Voer scraping uit
if (require.main === module) {
  alkmaarscraping();
}
