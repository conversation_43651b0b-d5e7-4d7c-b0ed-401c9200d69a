/**
 * Geocoder service
 * Zet plaatsnamen om naar coördinaten met behul<PERSON> van de Google Maps Geocoding API
 */

require('dotenv').config();
const axios = require('axios');

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY || process.env.GOOGLE_PLACES_API_KEY;

if (!GOOGLE_MAPS_API_KEY) {
  console.warn('⚠️ Geen Google API key gevonden in .env bestand. Geocoding zal niet werken.');
} else if (!process.env.GOOGLE_MAPS_API_KEY && process.env.GOOGLE_PLACES_API_KEY) {
  console.log('ℹ️ Gebruik GOOGLE_PLACES_API_KEY voor geocoding (GOOGLE_MAPS_API_KEY niet gevonden)');
}

/**
 * Zet een plaatsnaam om naar coördinaten
 * @param {string} address - <PERSON> plaatsnaam of adres om om te zetten
 * @returns {Promise<{lat: number, lng: number}|null>} - <PERSON> coördinaten of null bij een fout
 */
async function geocode(address) {
  if (!GOOGLE_MAPS_API_KEY) {
    throw new Error('Google API Key ontbreekt in .env bestand (GOOGLE_MAPS_API_KEY of GOOGLE_PLACES_API_KEY vereist)');
  }

  try {
    const response = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
      params: {
        address: address,
        key: GOOGLE_MAPS_API_KEY
      }
    });

    if (response.data.status === 'OK' && response.data.results.length > 0) {
      const location = response.data.results[0].geometry.location;
      return {
        lat: location.lat,
        lng: location.lng
      };
    } else {
      console.warn(`Geocoding fout: ${response.data.status}`);
      return null;
    }
  } catch (error) {
    console.error(`Geocoding fout: ${error.message}`);
    return null;
  }
}

module.exports = {
  geocode
}; 