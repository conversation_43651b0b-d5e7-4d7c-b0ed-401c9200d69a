/**
 * Hulpscript om pkg compatibiliteit te verbeteren
 * Voert dit script uit vóór het bouwen van de executable
 */
const fs = require('fs');
const path = require('path');

// Functie om package.json te updaten om problematische modules te includeren
function updatePackageJson() {
  const packageJsonPath = path.join(__dirname, 'package.json');
  const packageJson = require(packageJsonPath);
  
  // Maak pkg configuratie aan als die niet bestaat
  if (!packageJson.pkg) {
    packageJson.pkg = {};
  }
  
  if (!packageJson.pkg.assets) {
    packageJson.pkg.assets = [];
  }
  
  // Toevoegen van problematische modules als assets
  const modulesToInclude = [
    'node_modules/axios/**/*',
    'node_modules/follow-redirects/**/*',
    'node_modules/tslib/**/*',
    'node_modules/yargs/**/*',
    'node_modules/validator/**/*',
    'node_modules/inquirer/**/*'
  ];
  
  // Voeg modules toe die nog niet in de assets staan
  modulesToInclude.forEach(module => {
    if (!packageJson.pkg.assets.includes(module)) {
      packageJson.pkg.assets.push(module);
    }
  });
  
  // Schrijf terug naar bestand
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('Package.json bijgewerkt met extra module assets');
}

// Update package.json voor betere pkg-compatibiliteit
updatePackageJson();

console.log('Pkg-fix script voltooid. Je kunt nu `npm run build` uitvoeren.');