const axios = require('axios');

async function testWhopDirect() {
  const apiKey = 'o0WQ4u4rIcEhme24JzmMw7jFZ9eWkTgthPVh0WBWW-c';
  
  try {
    const response = await axios.get('https://api.whop.com/api/v2/me', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Success:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

testWhopDirect(); 