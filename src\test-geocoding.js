require('dotenv').config();
const axios = require('axios');

async function testGeocoding(location) {
  const apiKey = process.env.GOOGLE_PLACES_API_KEY;
  
  console.log(`Testing geocoding voor locatie: "${location}"`);
  
  try {
    const response = await axios.get(
      `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(location)}&key=${apiKey}`
    );
    
    console.log('\nAPI Response:');
    console.log(JSON.stringify(response.data, null, 2));
    
    if (response.data.status === 'OK') {
      const result = response.data.results[0];
      console.log('\nGevonden locatie:');
      console.log(`Adres: ${result.formatted_address}`);
      console.log(`Latitude: ${result.geometry.location.lat}`);
      console.log(`Longitude: ${result.geometry.location.lng}`);
    } else {
      console.error(`\nFout: ${response.data.status}`);
      if (response.data.error_message) {
        console.error(`Details: ${response.data.error_message}`);
      }
    }
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('API Response:', error.response.data);
    }
  }
}

// Test een locatie
const testLocation = process.argv[2] || 'Tumbak Bayuh, Bali';
testGeocoding(testLocation); 