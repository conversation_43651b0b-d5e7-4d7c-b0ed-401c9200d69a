const LicenseManager = require('./license');
const fs = require('fs').promises;
const path = require('path');

async function testLicenseFiles() {
  const licenseManager = new LicenseManager();

  console.log('\n=== License File Test ===');
  
  // 1. Test bestandslocaties
  const licenseFile = path.join(process.cwd(), 'license.key');
  const metaFile = path.join(process.cwd(), 'license.meta.json');
  
  console.log('\nBestandslocaties:');
  console.log('License file:', licenseFile);
  console.log('Metadata file:', metaFile);

  // 2. Check of bestanden bestaan
  try {
    await fs.access(licenseFile);
    console.log('\n✅ License file gevonden');
    
    const licenseContent = await fs.readFile(licenseFile, 'utf8');
    console.log('License inhoud:', licenseContent.substring(0, 50) + '...');
  } catch (error) {
    console.log('\n❌ License file niet gevonden:', error.message);
  }

  try {
    await fs.access(metaFile);
    console.log('\n✅ Metadata file gevonden');
    
    const metaContent = await fs.readFile(metaFile, 'utf8');
    const metadata = JSON.parse(metaContent);
    console.log('Metadata inhoud:', JSON.stringify(metadata, null, 2));
  } catch (error) {
    console.log('\n❌ Metadata file niet gevonden:', error.message);
  }

  // 3. Test licentie validatie
  console.log('\nLicentie validatie test:');
  
  const exists = await licenseManager.checkLicenseExists();
  console.log('checkLicenseExists():', exists);

  if (exists) {
    const metadata = await licenseManager.readMetadata();
    console.log('\nMetadata validatie:');
    console.log('- Machine ID validatie:', licenseManager.validateMachineId(metadata));
    console.log('- Offline validatie:', licenseManager.isValidOffline(metadata));
    
    console.log('\nHuidige machine info:');
    console.log('- Machine ID:', licenseManager.getMachineId());
    console.log('- Hostname:', require('os').hostname());
  }
}

// Start de test
testLicenseFiles().catch(console.error); 