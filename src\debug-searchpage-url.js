require('dotenv').config();
const { mapPlacesOnlyToAirtableFields } = require('./services/airtableService');
const Airtable = require('airtable');

// Airtable configuratie
const accessToken = process.env.AIRTABLE_ACCESS_TOKEN;
const baseId = process.env.AIRTABLE_BASE_ID;
const tableName = process.env.AIRTABLE_TABLE_NAME || 'Places';

if (!accessToken || !baseId) {
  console.error('Fout: AIRTABLE_ACCESS_TOKEN en AIRTABLE_BASE_ID moeten worden ingesteld in het .env bestand');
  process.exit(1);
}

const base = new Airtable({ apiKey: accessToken }).base(baseId);

async function debugSearchPageUrl() {
  console.log('🔍 Debug searchpage_url probleem...\n');

  // Test 1: Check mapping functie
  console.log('1. Test mapping functie...');
  const mockPlaceDetails = {
    name: 'Debug Test Bedrijf',
    formatted_address: 'Teststraat 123, Alkmaar, 1811 AB, Nederland',
    formatted_phone_number: '+31 72 123 4567',
    website: 'https://testbedrijf.nl',
    url: 'https://maps.google.com/?cid=12345678901234567890',
    place_id: 'ChIJtest123'
  };

  const mappedFields = mapPlacesOnlyToAirtableFields(mockPlaceDetails);
  console.log('Gemapte velden:');
  Object.entries(mappedFields).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });

  if (mappedFields.searchpage_url) {
    console.log('✅ searchpage_url is aanwezig in mapping');
  } else {
    console.log('❌ searchpage_url ontbreekt in mapping');
  }

  // Test 2: Check Airtable schema
  console.log('\n2. Check Airtable schema...');
  try {
    const testRecord = await base(tableName).create({
      company_name: 'Schema Test Bedrijf',
      searchpage_url: 'https://maps.google.com/?cid=test123',
      scrape_source: 'crawler_v1',
      scrape_timestamp_iso: new Date().toISOString()
    });
    
    console.log('✅ searchpage_url veld bestaat in Airtable');
    console.log(`Test record ID: ${testRecord.getId()}`);
    
    // Check of het veld daadwerkelijk is opgeslagen
    if (testRecord.fields.searchpage_url) {
      console.log(`✅ searchpage_url opgeslagen: ${testRecord.fields.searchpage_url}`);
    } else {
      console.log('❌ searchpage_url niet opgeslagen in record');
    }
    
  } catch (error) {
    console.error('❌ Fout bij testen Airtable schema:');
    console.error(`   ${error.message}`);
    if (error.error && error.error.type === 'INVALID_REQUEST_UNKNOWN_FIELD_NAME') {
      console.error('   → Het veld "searchpage_url" bestaat niet in Airtable!');
      console.error('   → Je moet dit veld handmatig toevoegen in Airtable');
    }
  }

  // Test 3: Check recent records voor searchpage_url
  console.log('\n3. Check recente records...');
  try {
    const records = await base(tableName).select({
      maxRecords: 3,
      sort: [{field: 'scrape_timestamp_iso', direction: 'desc'}]
    }).firstPage();
    
    console.log(`Gevonden ${records.length} recente records:`);
    records.forEach((record, index) => {
      console.log(`\nRecord ${index + 1} (${record.getId()}):`);
      console.log(`  company_name: ${record.fields.company_name}`);
      console.log(`  searchpage_url: ${record.fields.searchpage_url || 'ONTBREEKT'}`);
    });
    
  } catch (error) {
    console.error('❌ Fout bij ophalen recente records:', error.message);
  }
}

debugSearchPageUrl().catch(console.error);
