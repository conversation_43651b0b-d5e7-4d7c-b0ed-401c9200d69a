require('dotenv').config();
const { mapPlacesToAirtableFields } = require('./services/airtableService');
const Airtable = require('airtable');

// Airtable configuratie
const accessToken = process.env.AIRTABLE_ACCESS_TOKEN;
const baseId = process.env.AIRTABLE_BASE_ID;
const tableName = process.env.AIRTABLE_TABLE_NAME || 'Places';

if (!accessToken || !baseId) {
  console.error('Fout: AIRTABLE_ACCESS_TOKEN en AIRTABLE_BASE_ID moeten worden ingesteld in het .env bestand');
  process.exit(1);
}

const base = new Airtable({ apiKey: accessToken }).base(baseId);

async function testAirtableMapping() {
  console.log('🧪 Test Airtable mapping functionaliteit...\n');

  // Test data - simuleer Places API response
  const mockPlaceDetails = {
    name: 'Test Bedrijf BV',
    formatted_address: 'Teststraat 123, Amsterdam, 1234 AB, Nederland',
    formatted_phone_number: '+31 20 123 4567',
    website: 'https://testbedrijf.nl',
    place_id: 'ChIJtest123'
  };

  // Test data - simuleer Firecrawl response
  const mockEnrichedData = {
    contactPerson: {
      name: 'Jan de Tester',
      title: 'HR Manager',
      email: '<EMAIL>',
      phone: '+31 20 123 4568'
    },
    companySummary: 'Test bedrijf dat zich specialiseert in het testen van software applicaties.'
  };

  // Test de mapping functie
  console.log('1. Test mapping functie...');
  const mappedFields = mapPlacesToAirtableFields(mockPlaceDetails, mockEnrichedData);
  
  console.log('✅ Gemapte velden:');
  Object.entries(mappedFields).forEach(([key, value]) => {
    console.log(`   ${key}: ${value}`);
  });

  // Test schrijven naar Airtable
  if (process.argv.includes('--write-test')) {
    console.log('\n2. Test schrijven naar Airtable...');
    try {
      const createdRecord = await base(tableName).create(mappedFields);
      console.log('✅ Test record succesvol aangemaakt!');
      console.log(`   Record ID: ${createdRecord.getId()}`);
      console.log('   Velden in Airtable:');
      Object.entries(createdRecord.fields).forEach(([key, value]) => {
        console.log(`   ${key}: ${value}`);
      });
    } catch (error) {
      console.error('❌ Fout bij schrijven naar Airtable:');
      console.error(`   ${error.message}`);
      if (error.statusCode) {
        console.error(`   Status code: ${error.statusCode}`);
        console.error(`   Details: ${JSON.stringify(error.error || {})}`);
      }
    }
  } else {
    console.log('\n💡 Gebruik --write-test om daadwerkelijk naar Airtable te schrijven');
  }
}

testAirtableMapping().catch(console.error);
