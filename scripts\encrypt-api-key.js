require('dotenv').config();
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

function encryptApiKey(apiKey, outputFile) {
  if (!apiKey) {
    throw new Error('WHOP_API_KEY is niet gevonden in .env bestand');
  }

  // Genereer een sterke encryptiesleutel en IV
  const encryptionKey = crypto.randomBytes(32);
  const iv = crypto.randomBytes(16);
  
  // Gebruik createCipheriv in plaats van createCipher
  const cipher = crypto.createCipheriv('aes-256-gcm', encryptionKey, iv);
  const encrypted = Buffer.concat([cipher.update(apiKey, 'utf8'), cipher.final()]);
  const authTag = cipher.getAuthTag();
  
  // Maak de output directory als die niet bestaat
  const outputDir = path.dirname(outputFile);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // Sla de versleutelde key op in de source code
  const code = `
    // Auto-generated, do not edit
    module.exports = {
      encryptedKey: '${encrypted.toString('hex')}',
      iv: '${iv.toString('hex')}',
      authTag: '${authTag.toString('hex')}',
      key: Buffer.from('${encryptionKey.toString('hex')}', 'hex')
    };
  `;
  
  fs.writeFileSync(outputFile, code);
  console.log(`✅ API key versleuteld en opgeslagen in ${outputFile}`);
}

try {
  const apiKey = process.env.WHOP_API_KEY;
  if (!apiKey) {
    console.error('❌ WHOP_API_KEY niet gevonden in .env bestand');
    console.log('Controleer of:');
    console.log('1. Het .env bestand bestaat');
    console.log('2. WHOP_API_KEY correct is ingesteld');
    process.exit(1);
  }

  encryptApiKey(apiKey, path.join(__dirname, '../src/encrypted-keys.js'));
} catch (error) {
  console.error('❌ Fout bij versleutelen:', error.message);
  process.exit(1);
} 