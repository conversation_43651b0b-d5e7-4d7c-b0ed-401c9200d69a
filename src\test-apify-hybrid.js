require('dotenv').config();
const ApifyScraper = require('./apifyScraper');

async function testApifyHybrid() {
  console.log('🚀 Test Apify hybride scraper (Apify + beperkte Firecrawl)...\n');

  // Test locatie en zoektermen
  const location = 'Utrecht, Netherlands';
  const keywords = ['bakery']; // Kleine test set

  console.log('📋 Test configuratie:');
  console.log(`📍 Locatie: ${location}`);
  console.log(`🔑 Zoektermen: ${keywords.join(', ')}`);
  console.log(`🔄 Modus: Apify hybride scraping (Apify API + beperkte Firecrawl)`);
  console.log(`📊 Max plaatsen per zoekterm: 3\n`);

  // Configureer scraper voor hybride test
  const scraper = new ApifyScraper({
    delayBetweenRequests: 2000, // Langzamer vanwege Firecrawl
    maxPlacesPerSearch: 3, // Zeer beperkt voor test
    enableFirecrawlEnrichment: true, // Firecrawl ingeschakeld
    firecrawlMode: 'limited', // Hybride modus
    logLevel: 'info',
    language: 'nl',
    website: 'onlyPlacesWithWebsite', // Alleen plaatsen met website
    searchMatching: 'all',
    skipClosedPlaces: false
  });

  try {
    console.log('🔍 Starten van Apify hybride test...\n');
    
    await scraper.run({
      location: location,
      keywords: keywords
    });

    console.log('\n✅ Apify hybride test voltooid!');
    console.log('\n📊 Resultaten:');
    console.log(`- Totaal verwerkt: ${scraper.totalProcessed}`);
    console.log(`- Totaal opgeslagen: ${scraper.totalSaved}`);
    console.log(`- Totaal verrijkt: ${scraper.totalEnriched}`);
    console.log(`- Totaal fouten: ${scraper.totalErrors}`);
    
    console.log('\n💡 Apify hybride voordelen:');
    console.log('- Apify data + contactgegevens');
    console.log('- Bedrijfssamenvatting en contactpersoon');
    console.log('- Ideaal voor lead generatie');
    
  } catch (error) {
    console.error('\n❌ Fout tijdens Apify hybride test:', error.message);
    console.error(error.stack);
  }
}

// Voer test uit
if (require.main === module) {
  testApifyHybrid();
}

module.exports = testApifyHybrid;
