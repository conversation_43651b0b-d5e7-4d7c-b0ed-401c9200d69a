# Apify API Integratie

## Overzicht

De Apify API integratie biedt een alternatief voor de Google Places API door gebruik te maken van de Apify Google Maps Scraper. Deze integratie biedt verschillende voordelen en nieuwe mogelijkheden.

## Voordelen van Apify

### 1. Geen 60 Resultaten Limiet
- Google Places API heeft een limiet van 60 resultaten per zoekopdracht
- Apify kan veel meer resultaten ophalen per zoekopdracht (tot 500+)
- Geen grid-based searching nodig

### 2. Meer Gedetailleerde Data
- Bedrijfsbeschrijvingen
- Gedetailleerde categorieën en subcategorieën
- Prijs niveau informatie
- Advertentie status
- Zoekterm ranking

### 3. Betere Lokale Coverage
- Apify scrapt direct van Google Maps
- Meer lokale bedrijven worden gevonden
- Betere coverage van kleinere bedrijven

## Configuratie

### Environment Variabelen

Voeg de volgende variabelen toe aan je `.env` bestand:

```env
# Apify Configuration
APIFY_API_TOKEN=your_apify_api_token_here
APIFY_ACTOR_ID=2Mdma1N6Fd0y3QEjR
```

### API Token Verkrijgen

1. Ga naar [Apify Console](https://console.apify.com/)
2. Maak een account aan of log in
3. Ga naar Settings > Integrations
4. Kopieer je API token
5. Voeg het toe aan je `.env` bestand

## Gebruik

### 1. Snelle Apify Modus (Alleen Apify Data)

```javascript
const ApifyScraper = require('./apifyScraper');

const scraper = new ApifyScraper({
  delayBetweenRequests: 2000,
  maxPlacesPerSearch: 50,
  enableFirecrawlEnrichment: false, // Snelle modus
  language: 'nl',
  website: 'allPlaces'
});

await scraper.run({
  location: 'Amsterdam, Netherlands',
  keywords: ['restaurant', 'bakery']
});
```

### 2. Apify Hybride Modus (Apify + Firecrawl)

```javascript
const scraper = new ApifyScraper({
  delayBetweenRequests: 3000,
  maxPlacesPerSearch: 50,
  enableFirecrawlEnrichment: true,
  firecrawlMode: 'limited', // Hybride modus
  language: 'nl',
  website: 'onlyPlacesWithWebsite' // Alleen plaatsen met website
});

await scraper.run({
  location: 'Amsterdam, Netherlands',
  keywords: ['restaurant', 'bakery']
});
```

## CLI Gebruik

### Interactieve CLI

```bash
node src/cli.js
```

Kies uit de nieuwe opties:
- "Zoeken op locatie (Apify - snel)"
- "Zoeken op locatie (Apify hybride - beperkte Firecrawl)"

### Direct Scripts

```bash
# Snelle Apify modus
node src/apify-alkmaar-scraping.js

# Hybride Apify modus
node src/apify-alkmaar-hybrid-scraping.js
```

## Data Mapping

### Apify Data Velden

De Apify API levert de volgende extra velden:

- `description` - Bedrijfsbeschrijving
- `categoryName` - Hoofdcategorie
- `subCategoryName` - Subcategorie
- `priceLevel` - Prijsniveau (€, ££, €€€)
- `isAdvertisement` - Of het een advertentie is
- `rank` - Positie in zoekresultaten
- `searchString` - Gebruikte zoekterm

### Airtable Mapping

Apify data wordt gemapped naar Airtable met:
- `scrape_source`: 'apify_v1'
- Alle standaard velden (naam, adres, telefoon, website)
- Extra informatie in `notes` veld
- Apify specifieke data behouden

## Testing

### Basis Test

```bash
node src/test-apify.js
```

### Hybride Test

```bash
node src/test-apify-hybrid.js
```

## Kosten Overwegingen

### Apify Kosten
- Apify rekent per API call
- Ongeveer $0.001 per plaats
- 1000 plaatsen = ~$1
- Bulk operaties zijn kosteneffectiever

### Vergelijking met Google Places API
- Google Places API: $17 per 1000 requests
- Apify: ~$1 per 1000 plaatsen
- Apify is significant goedkoper

## Rate Limits

### Apify Rate Limits
- Standaard: 200 requests per minuut
- Pro accounts: 1000+ requests per minuut
- Gebruik `delayBetweenRequests` om limits te respecteren

### Aanbevolen Settings
- Snelle modus: 2000ms delay
- Hybride modus: 3000ms delay (vanwege Firecrawl)

## Troubleshooting

### Veelvoorkomende Problemen

1. **API Token Fout**
   - Controleer of APIFY_API_TOKEN correct is ingesteld
   - Verifieer dat het token actief is

2. **Rate Limit Errors**
   - Verhoog `delayBetweenRequests`
   - Verlaag `maxPlacesPerSearch`

3. **Geen Resultaten**
   - Controleer locatie spelling
   - Probeer bredere zoektermen
   - Verifieer Actor ID

### Debug Mode

```javascript
const scraper = new ApifyScraper({
  logLevel: 'debug' // Meer gedetailleerde logging
});
```

## Migratie van Google Places API

### Stappen voor Migratie

1. Installeer Apify client: `npm install apify-client`
2. Voeg Apify configuratie toe aan `.env`
3. Test met kleine dataset
4. Migreer geleidelijk

### Data Compatibiliteit

Apify data is grotendeels compatibel met bestaande Google Places API data:
- Zelfde Airtable schema
- Vergelijkbare veld mapping
- Bestaande cache systeem werkt

## Best Practices

### 1. Batch Processing
- Verwerk meerdere zoektermen tegelijk
- Gebruik deduplicatie om duplicaten te voorkomen

### 2. Cache Management
- Apify gebruikt eigen cache bestanden
- `processed_apify_places.json`
- `processed_apify_urls.json`

### 3. Error Handling
- Implementeer retry logic
- Monitor API quota
- Log alle errors voor debugging

### 4. Data Quality
- Gebruik `website: 'onlyPlacesWithWebsite'` voor Firecrawl
- Filter op minimum rating indien gewenst
- Valideer resultaten voordat opslaan
