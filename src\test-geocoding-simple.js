require('dotenv').config();
const { geocode } = require('./services/geocoder');

async function testGeocode() {
  try {
    console.log('🧪 Test geocoding voor Amsterdam...');
    const result = await geocode('Amsterdam');
    
    if (result) {
      console.log('✅ Geocoding succesvol!');
      console.log(`Latitude: ${result.lat}`);
      console.log(`Longitude: ${result.lng}`);
    } else {
      console.log('❌ Geen resultaat ontvangen');
    }
  } catch (error) {
    console.error('❌ Geocoding fout:', error.message);
  }
}

testGeocode();
