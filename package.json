{"name": "places-api-scraper", "version": "1.0.0", "description": "Tool voor het scrapen en verrijken van bedrijfsinformatie via Google Places API en andere bronnen", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "build": "node build.js", "lint": "eslint src/**/*.js", "format": "prettier --write \"src/**/*.js\""}, "dependencies": {"airtable": "^0.12.2", "axios": "^1.6.2", "commander": "^13.1.0", "dotenv": "^16.3.1", "express": "^4.18.2", "googleapis": "^129.0.0", "inquirer": "^8.2.5", "node-machine-id": "^1.1.12", "openai": "^4.20.1", "pkg": "^5.8.1", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0"}, "author": "", "license": "UNLICENSED", "private": true}