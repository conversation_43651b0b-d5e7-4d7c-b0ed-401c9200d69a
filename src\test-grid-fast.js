require('dotenv').config();
const PlacesScraper = require('./scraper');

async function testGridFast() {
  console.log('🚀 Test snelle grid-based scraping (alleen Places API)...\n');

  // Amsterdam centrum coördinaten
  const center = { lat: 52.3676, lng: 4.9041 };
  const radiusKm = 1; // Kleine radius voor snelle test
  const keywords = ['painter']; // Schilder

  console.log('📋 Test configuratie:');
  console.log(`📍 Locatie: Amsterdam centrum (${center.lat}, ${center.lng})`);
  console.log(`⭕ Radius: ${radiusKm} km`);
  console.log(`🔑 Zoektermen: ${keywords.join(', ')}`);
  console.log(`🚀 Snelle modus: Alleen Places API (geen Firecrawl)\n`);

  // Configureer scraper voor snelle modus
  const scraper = new PlacesScraper({
    delayBetweenRequests: 200,
    enableFirecrawlEnrichment: false, // Firecrawl uitgeschakeld
    logLevel: 'info',
    maxPlacesPerGrid: 20 // Beperk voor test
  });

  try {
    console.log('🔍 Starten van snelle scraper...\n');
    
    await scraper.run({
      center: center,
      radiusKm: radiusKm,
      keywords: keywords,
      gridSizeM: 500
    });

    console.log('\n✅ Snelle test voltooid!');
    
  } catch (error) {
    console.error('\n❌ Fout tijdens snelle test:', error.message);
    console.error(error.stack);
  }
}

// Voer test uit als dit script direct wordt aangeroepen
if (require.main === module) {
  testGridFast();
}
