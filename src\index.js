#!/usr/bin/env node
/**
 * Places API Scraper CLI Tool
 * Flexibele tool voor het scrapen van Google Places API data
 */

require('dotenv').config();
const fs = require('fs').promises;
const path = require('path');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const { GridGenerator } = require('./utils/gridGenerator');
const PlacesScraper = require('./scraper');

// Bovenaan het bestand
function getBasePath() {
  // In development, gebruik process.cwd()
  if (process.pkg === undefined) {
    return process.cwd();
  }
  // In executable, gebruik de directory van de exe
  return path.dirname(process.execPath);
}

// Update de pad definities
const BASE_DIR = getBasePath();
const CONFIG_DIR = path.join(BASE_DIR, 'config');
const DATA_DIR = path.join(BASE_DIR, 'data');
const LOGS_DIR = path.join(BASE_DIR, 'logs');

// Functie om configuratiebestand te laden
async function loadConfig(configFile) {
  try {
    const configPath = path.resolve(process.cwd(), configFile);
    const configData = await fs.readFile(configPath, 'utf8');
    return JSON.parse(configData);
  } catch (error) {
    console.error(`Fout bij laden configuratie: ${error.message}`);
    process.exit(1);
  }
}

// Functie om directory te controleren en aan te maken indien nodig
async function ensureDirectoryExists(dir) {
  try {
    await fs.mkdir(dir, { recursive: true });
  } catch (err) {
    if (err.code !== 'EEXIST') throw err;
  }
}

// Hulpfunctie om argumenten vanaf commandoregel te parsen
function parseArrayArg(arg) {
  if (!arg) return null;
  return Array.isArray(arg) ? arg : [arg];
}

class PlacesApiScraper {
  constructor() {
    this.initialized = false;
    this.gridGenerator = null;
    this.scraper = null;
  }

  async initialize() {
    if (this.initialized) return;

    try {
      // Maak directories aan
      await Promise.all([
        fs.mkdir(CONFIG_DIR, { recursive: true }),
        fs.mkdir(DATA_DIR, { recursive: true }),
        fs.mkdir(LOGS_DIR, { recursive: true })
      ]);

      // Initialiseer services
      this.gridGenerator = new GridGenerator();
      this.scraper = new PlacesScraper({
        delayBetweenRequests: 1000,
        maxPlacesPerGrid: 20,
        maxRetries: 3,
        logLevel: 'info'
      });

      this.initialized = true;
    } catch (error) {
      console.error('Fout bij initialiseren:', error);
      throw error;
    }
  }

  async generateGrid(location, keywords = []) {
    if (!this.initialized) {
      await this.initialize();
    }

    console.log('Grid genereren voor locatie:', location);
    try {
      const gridCoordinates = await this.gridGenerator.generateGrid(location);
      console.log(`Grid gegenereerd met ${gridCoordinates.length} cellen`);
      
      const cleanName = location.replace(/[^a-z0-9]/gi, '_').toLowerCase();
      const filename = `${cleanName}.json`;
      
      const configPath = path.join(BASE_DIR, 'config', filename);
      console.log('Configuratie opslaan in:', configPath);

      // Zorg dat de config directory bestaat
      await fs.mkdir(path.dirname(configPath), { recursive: true });
      
      const config = {
        name: location,
        gridCoordinates,
        keywords: keywords // Sla zoektermen op in configuratie
      };
      
      await fs.writeFile(configPath, JSON.stringify(config, null, 2));
      console.log('✅ Configuratie opgeslagen met', keywords.length, 'zoektermen');
      
      return gridCoordinates;
    } catch (error) {
      console.error('Grid generatie fout details:', error);
      throw new Error(`Grid generatie fout: ${error.message}`);
    }
  }

  async scrapeLocations(config) {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      await this.scraper.initialize();
      await this.scraper.searchGrids(config.gridCoordinates, config.keywords);
      await this.scraper.logger.saveLogsToFile();
    } catch (error) {
      throw new Error(`Scraping fout: ${error.message}`);
    }
  }
}

// Exporteer een singleton instance
const instance = new PlacesApiScraper();
module.exports = instance;

// Als het script direct wordt uitgevoerd (niet geïmporteerd)
if (require.main === module) {
  // Parse command line arguments en voer de juiste actie uit
  const argv = require('yargs/yargs')(process.argv.slice(2))
    .option('config', { type: 'string' })
    .option('location', { type: 'string' })
    .argv;

  (async () => {
    try {
      if (argv.location) {
        await module.exports.generateGrid(argv.location);
      } else if (argv.config) {
        const config = require(path.resolve(argv.config));
        await module.exports.scrapeLocations(config);
      }
    } catch (error) {
      console.error('Error:', error.message);
      process.exit(1);
    }
  })();
}